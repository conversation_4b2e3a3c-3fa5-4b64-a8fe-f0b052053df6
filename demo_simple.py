"""
个人文档管理系统简单演示
Personal Document Management System Simple Demo

不依赖外部库的基础功能演示
"""

import sys
import os
import mimetypes
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


class SimpleFileDetector:
    """简化版文件类型检测器"""
    
    # 文件头标志字典
    FILE_SIGNATURES = {
        'pdf': [b'%PDF'],
        'doc': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],
        'docx': [b'PK\x03\x04'],
        'txt': [],  # 文本文件通过其他方式检测
        'md': [],
        'html': [b'<!DOCTYPE', b'<html', b'<HTML'],
        'xml': [b'<?xml'],
    }
    
    SUPPORTED_TYPES = {'pdf', 'doc', 'docx', 'txt', 'md', 'html', 'xml'}
    
    def read_file_header(self, file_path: Path, max_bytes: int = 512) -> bytes:
        """读取文件头部字节"""
        try:
            with open(file_path, 'rb') as f:
                return f.read(max_bytes)
        except Exception as e:
            print(f"读取文件头失败 {file_path}: {e}")
            return b''
    
    def detect_by_signature(self, file_path: Path) -> str:
        """通过文件头标志检测文件类型"""
        header = self.read_file_header(file_path)
        if not header:
            return 'unknown'
        
        for file_type, signatures in self.FILE_SIGNATURES.items():
            for signature in signatures:
                if header.startswith(signature):
                    return file_type
        
        # 检查是否为文本文件
        if self._is_text_file(header):
            extension = file_path.suffix.lower().lstrip('.')
            if extension in ['md', 'txt', 'html', 'xml']:
                return extension
            return 'txt'
        
        return 'unknown'
    
    def _is_text_file(self, header: bytes) -> bool:
        """判断是否为文本文件"""
        try:
            header.decode('utf-8')
            return True
        except UnicodeDecodeError:
            pass
        
        try:
            header.decode('gbk')
            return True
        except UnicodeDecodeError:
            pass
        
        # 检查是否大部分字符都是可打印的
        if not header:
            return False
        printable_count = sum(1 for byte in header if 32 <= byte <= 126 or byte in [9, 10, 13])
        return printable_count / len(header) > 0.7
    
    def detect_file_type(self, file_path: Path) -> tuple:
        """检测文件类型"""
        if not file_path.exists():
            return 'unknown', 'file_not_found', 0.0
        
        detected_type = self.detect_by_signature(file_path)
        if detected_type != 'unknown':
            return detected_type, 'signature', 0.9
        
        # 通过扩展名检测
        extension = file_path.suffix.lower().lstrip('.')
        if extension in self.SUPPORTED_TYPES:
            return extension, 'extension', 0.5
        
        return 'unknown', 'no_match', 0.0
    
    def is_document_file(self, file_path: Path) -> bool:
        """判断是否为文档文件"""
        file_type, _, confidence = self.detect_file_type(file_path)
        return file_type in self.SUPPORTED_TYPES and confidence > 0.0


def demo_file_detection():
    """演示文件类型检测功能"""
    print("=== 文件类型检测演示 ===")
    
    detector = SimpleFileDetector()
    
    # 获取当前目录下的一些文件进行测试
    current_dir = Path(".")
    test_files = [
        current_dir / "README.md",
        current_dir / "pyproject.toml", 
        current_dir / "LICENSE",
        current_dir / "main.py",
        current_dir / "demo_simple.py"
    ]
    
    for file_path in test_files:
        if file_path.exists():
            print(f"\n检测文件: {file_path}")
            file_type, method, confidence = detector.detect_file_type(file_path)
            is_doc = detector.is_document_file(file_path)
            
            print(f"  文件类型: {file_type}")
            print(f"  检测方法: {method}")
            print(f"  置信度: {confidence}")
            print(f"  是否为文档: {is_doc}")
            
            # 显示文件大小
            try:
                size = file_path.stat().st_size
                print(f"  文件大小: {size} 字节")
            except Exception:
                pass


def demo_directory_scan():
    """演示目录扫描功能"""
    print("\n=== 目录扫描演示 ===")
    
    detector = SimpleFileDetector()
    current_dir = Path(".")
    
    print(f"扫描目录: {current_dir.absolute()}")
    
    total_files = 0
    document_files = 0
    total_size = 0
    file_types = {}
    
    try:
        for item in current_dir.iterdir():
            if item.is_file():
                total_files += 1
                
                try:
                    size = item.stat().st_size
                    total_size += size
                except Exception:
                    size = 0
                
                if detector.is_document_file(item):
                    document_files += 1
                    file_type, _, _ = detector.detect_file_type(item)
                    file_types[file_type] = file_types.get(file_type, 0) + 1
    
    except Exception as e:
        print(f"扫描目录时出错: {e}")
        return
    
    print(f"\n目录统计信息:")
    print(f"  总文件数: {total_files}")
    print(f"  总大小: {total_size} 字节")
    print(f"  文档文件数: {document_files}")
    print(f"  文件类型分布: {file_types}")


def demo_project_structure():
    """演示项目结构"""
    print("\n=== 项目结构演示 ===")
    
    def show_tree(path: Path, prefix: str = "", max_depth: int = 3, current_depth: int = 0):
        """显示目录树"""
        if current_depth >= max_depth:
            return
        
        try:
            items = sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name.lower()))
            for i, item in enumerate(items):
                is_last = i == len(items) - 1
                current_prefix = "└── " if is_last else "├── "
                print(f"{prefix}{current_prefix}{item.name}")
                
                if item.is_dir() and not item.name.startswith('.'):
                    extension = "    " if is_last else "│   "
                    show_tree(item, prefix + extension, max_depth, current_depth + 1)
        except PermissionError:
            print(f"{prefix}    [权限不足]")
    
    current_dir = Path(".")
    print(f"项目根目录: {current_dir.absolute()}")
    show_tree(current_dir)


def main():
    """主函数"""
    print("个人文档管理系统 - 简单演示")
    print("Personal Document Management System - Simple Demo")
    print("=" * 50)
    
    try:
        demo_project_structure()
        demo_file_detection()
        demo_directory_scan()
        
        print("\n" + "=" * 50)
        print("演示完成！")
        print("Demo completed!")
        
        print("\n下一步开发计划:")
        print("1. 安装项目依赖: pip install -r requirements.txt (或使用Poetry)")
        print("2. 实现文档内容解析功能")
        print("3. 开发PySide6用户界面")
        print("4. 添加NLP功能（关键词提取、摘要生成）")
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
