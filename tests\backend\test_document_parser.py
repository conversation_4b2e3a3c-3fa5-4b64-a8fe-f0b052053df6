"""
文档解析器测试
Document Parser Tests
"""

import pytest
import tempfile
from pathlib import Path
from backend.core.document_parser import (
    DocumentParserManager, 
    TextDocumentParser, 
    DocumentParseResult
)


class TestDocumentParser:
    """文档解析器测试类"""
    
    def setup_method(self):
        """测试前的设置"""
        self.parser_manager = DocumentParserManager()
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def teardown_method(self):
        """测试后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_file(self, filename: str, content: str) -> Path:
        """创建测试文件"""
        file_path = self.temp_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_text_parser_basic(self):
        """测试基本文本解析"""
        content = """# 测试文档标题
        
这是一个测试文档的内容。
包含中文和English混合内容。

## 第一章
这里是第一章的内容。

## 第二章  
这里是第二章的内容。
"""
        
        txt_file = self.create_test_file('test.txt', content)
        result = self.parser_manager.parse_document(txt_file)
        
        assert result.success == True
        assert len(result.content) > 0
        assert result.word_count > 0
        assert result.language in ['zh', 'mixed']
        assert result.page_count >= 1
    
    def test_markdown_parser(self):
        """测试Markdown解析"""
        content = """# Markdown测试文档

这是一个**Markdown**文档的测试内容。

## 功能特性

- 支持中文
- 支持English
- 支持*斜体*和**粗体**

### 代码示例

```python
def hello_world():
    print("Hello, World!")
```

## 总结

这是一个测试文档。
"""
        
        md_file = self.create_test_file('test.md', content)
        result = self.parser_manager.parse_document(md_file)
        
        assert result.success == True
        assert '# Markdown测试文档' in result.content
        assert result.metadata.get('title') == 'Markdown测试文档'
        assert result.word_count > 0
    
    def test_encoding_detection(self):
        """测试编码检测"""
        parser = TextDocumentParser()
        
        # 测试UTF-8文件
        utf8_content = "这是UTF-8编码的中文内容"
        utf8_file = self.create_test_file('utf8.txt', utf8_content)
        encoding = parser.detect_encoding(utf8_file)
        assert encoding == 'utf-8'
    
    def test_language_detection(self):
        """测试语言检测"""
        parser = TextDocumentParser()
        
        # 中文内容
        chinese_text = "这是一段中文内容，用于测试语言检测功能。"
        assert parser._detect_language(chinese_text) == 'zh'
        
        # 英文内容
        english_text = "This is an English text for testing language detection."
        assert parser._detect_language(english_text) == 'en'
        
        # 混合内容
        mixed_text = "这是中英文混合的内容 with English words mixed in."
        assert parser._detect_language(mixed_text) in ['zh', 'mixed']
    
    def test_unsupported_format(self):
        """测试不支持的格式"""
        # 创建一个二进制文件
        binary_file = self.temp_dir / 'test.bin'
        with open(binary_file, 'wb') as f:
            f.write(b'\x00\x01\x02\x03\x04\x05')
        
        result = self.parser_manager.parse_document(binary_file)
        assert result.success == False
        assert "不支持的文档格式" in result.error_message
    
    def test_nonexistent_file(self):
        """测试不存在的文件"""
        nonexistent_file = self.temp_dir / 'nonexistent.txt'
        result = self.parser_manager.parse_document(nonexistent_file)
        assert result.success == False
    
    def test_empty_file(self):
        """测试空文件"""
        empty_file = self.create_test_file('empty.txt', '')
        result = self.parser_manager.parse_document(empty_file)
        
        assert result.success == True
        assert result.content == ''
        assert result.word_count == 0
    
    def test_large_file(self):
        """测试大文件"""
        # 创建一个较大的文件
        large_content = "这是一行测试内容。\n" * 1000
        large_file = self.create_test_file('large.txt', large_content)
        
        result = self.parser_manager.parse_document(large_file)
        assert result.success == True
        assert result.word_count > 1000
        assert result.page_count > 1
    
    def test_special_characters(self):
        """测试特殊字符"""
        content = """测试特殊字符：
        
中文标点：，。！？；：""''
英文标点：,.!?;:""''
数字：1234567890
符号：@#$%^&*()_+-=[]{}|\\:";'<>?,./
        
Emoji: 😀😃😄😁😆😅😂🤣
"""
        
        special_file = self.create_test_file('special.txt', content)
        result = self.parser_manager.parse_document(special_file)
        
        assert result.success == True
        assert len(result.content) > 0
        assert result.word_count > 0
    
    def test_get_supported_extensions(self):
        """测试获取支持的扩展名"""
        extensions = self.parser_manager.get_supported_extensions()
        
        assert '.txt' in extensions
        assert '.md' in extensions
        assert '.pdf' in extensions
        assert '.docx' in extensions
        assert len(extensions) > 0


class TestDocumentParseResult:
    """文档解析结果测试类"""
    
    def test_word_count_calculation(self):
        """测试字数计算"""
        result = DocumentParseResult()
        
        # 测试中英文混合
        result.content = "这是中文内容 with English words 123"
        result.calculate_word_count()
        
        # 应该统计中文字符和英文单词
        assert result.word_count > 0
        
        # 测试空内容
        result.content = ""
        result.calculate_word_count()
        assert result.word_count == 0
    
    def test_result_initialization(self):
        """测试结果初始化"""
        result = DocumentParseResult()
        
        assert result.success == False
        assert result.content == ""
        assert result.metadata == {}
        assert result.error_message == ""
        assert result.page_count == 0
        assert result.word_count == 0
        assert result.language == "unknown"
        assert result.encoding == "utf-8"


if __name__ == '__main__':
    pytest.main([__file__])
