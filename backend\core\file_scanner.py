"""
文件扫描器
File Scanner

扫描指定目录下的文档文件
"""

import os
import hashlib
from pathlib import Path
from typing import List, Dict, Generator, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from loguru import logger

from backend.utils.file_detector import file_detector


class FileScanner:
    """文件扫描器类"""
    
    def __init__(self, max_workers: int = 4):
        """
        初始化文件扫描器
        
        Args:
            max_workers: 最大并发线程数
        """
        self.max_workers = max_workers
        self.excluded_dirs = {
            '.git', '.svn', '.hg', '__pycache__', '.pytest_cache',
            'node_modules', '.vscode', '.idea', 'Thumbs.db', '.DS_Store'
        }
        self.excluded_extensions = {
            '.tmp', '.temp', '.log', '.cache', '.bak', '.swp'
        }
        logger.info(f"文件扫描器初始化完成，最大并发数: {max_workers}")
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """
        计算文件的MD5哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: MD5哈希值
        """
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                # 分块读取大文件
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.warning(f"计算文件哈希失败 {file_path}: {e}")
            return ""
    
    def should_skip_directory(self, dir_path: Path) -> bool:
        """
        判断是否应该跳过某个目录
        
        Args:
            dir_path: 目录路径
            
        Returns:
            bool: 是否跳过
        """
        dir_name = dir_path.name.lower()
        
        # 跳过隐藏目录
        if dir_name.startswith('.'):
            return True
        
        # 跳过排除的目录
        if dir_name in self.excluded_dirs:
            return True
        
        # 跳过系统目录
        if dir_name in {'system volume information', 'recycler', '$recycle.bin'}:
            return True
        
        return False
    
    def should_skip_file(self, file_path: Path) -> bool:
        """
        判断是否应该跳过某个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否跳过
        """
        file_name = file_path.name.lower()
        
        # 跳过隐藏文件
        if file_name.startswith('.'):
            return True
        
        # 跳过排除的扩展名
        if file_path.suffix.lower() in self.excluded_extensions:
            return True
        
        # 跳过过小的文件（小于1KB）
        try:
            if file_path.stat().st_size < 1024:
                return True
        except Exception:
            return True
        
        return False
    
    def scan_directory(self, directory: Path, recursive: bool = True) -> Generator[Path, None, None]:
        """
        扫描目录中的所有文件
        
        Args:
            directory: 要扫描的目录
            recursive: 是否递归扫描子目录
            
        Yields:
            Path: 文件路径
        """
        if not directory.exists() or not directory.is_dir():
            logger.warning(f"目录不存在或不是目录: {directory}")
            return
        
        try:
            for item in directory.iterdir():
                if item.is_file():
                    if not self.should_skip_file(item):
                        yield item
                elif item.is_dir() and recursive:
                    if not self.should_skip_directory(item):
                        yield from self.scan_directory(item, recursive)
        except PermissionError:
            logger.warning(f"没有权限访问目录: {directory}")
        except Exception as e:
            logger.error(f"扫描目录时出错 {directory}: {e}")
    
    def filter_document_files(self, file_paths: List[Path]) -> List[Dict]:
        """
        过滤出文档文件并获取详细信息
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            List[Dict]: 文档文件信息列表
        """
        document_files = []
        
        def process_file(file_path: Path) -> Optional[Dict]:
            try:
                # 检测文件类型
                if file_detector.is_document_file(file_path):
                    file_info = file_detector.get_file_info(file_path)
                    # 计算文件哈希
                    file_info['file_hash'] = self.calculate_file_hash(file_path)
                    return file_info
            except Exception as e:
                logger.warning(f"处理文件失败 {file_path}: {e}")
            return None
        
        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_path = {executor.submit(process_file, path): path for path in file_paths}
            
            # 使用tqdm显示进度
            with tqdm(total=len(file_paths), desc="检测文档文件") as pbar:
                for future in as_completed(future_to_path):
                    result = future.result()
                    if result:
                        document_files.append(result)
                    pbar.update(1)
        
        logger.info(f"扫描完成，找到 {len(document_files)} 个文档文件")
        return document_files
    
    def scan_for_documents(
        self, 
        directories: List[Path], 
        recursive: bool = True,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[Dict]:
        """
        扫描多个目录中的文档文件
        
        Args:
            directories: 要扫描的目录列表
            recursive: 是否递归扫描
            progress_callback: 进度回调函数 (current, total)
            
        Returns:
            List[Dict]: 文档文件信息列表
        """
        logger.info(f"开始扫描 {len(directories)} 个目录")
        
        # 收集所有文件
        all_files = []
        for directory in directories:
            logger.info(f"扫描目录: {directory}")
            files_in_dir = list(self.scan_directory(directory, recursive))
            all_files.extend(files_in_dir)
            logger.info(f"目录 {directory} 中找到 {len(files_in_dir)} 个文件")
        
        logger.info(f"总共找到 {len(all_files)} 个文件，开始文档类型检测")
        
        # 过滤文档文件
        document_files = self.filter_document_files(all_files)
        
        return document_files
    
    def get_directory_stats(self, directory: Path) -> Dict:
        """
        获取目录统计信息
        
        Args:
            directory: 目录路径
            
        Returns:
            Dict: 统计信息
        """
        stats = {
            'total_files': 0,
            'total_size': 0,
            'document_files': 0,
            'document_size': 0,
            'file_types': {},
            'largest_file': None,
            'largest_file_size': 0
        }
        
        try:
            for file_path in self.scan_directory(directory):
                stats['total_files'] += 1
                
                try:
                    file_size = file_path.stat().st_size
                    stats['total_size'] += file_size
                    
                    # 记录最大文件
                    if file_size > stats['largest_file_size']:
                        stats['largest_file_size'] = file_size
                        stats['largest_file'] = str(file_path)
                    
                    # 检测文档文件
                    if file_detector.is_document_file(file_path):
                        stats['document_files'] += 1
                        stats['document_size'] += file_size
                        
                        # 统计文件类型
                        file_type, _, _ = file_detector.detect_file_type(file_path)
                        stats['file_types'][file_type] = stats['file_types'].get(file_type, 0) + 1
                
                except Exception as e:
                    logger.debug(f"获取文件信息失败 {file_path}: {e}")
                    
        except Exception as e:
            logger.error(f"获取目录统计信息失败 {directory}: {e}")
        
        return stats


# 全局文件扫描器实例
file_scanner = FileScanner()
