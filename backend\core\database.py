"""
数据库连接和配置模块
Database Connection and Configuration Module

提供SQLite数据库的连接、初始化和管理功能
"""

import os
import sqlite3
import time
from pathlib import Path
from typing import Optional
from sqlalchemy import create_engine, Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from loguru import logger

# SQLAlchemy基类
Base = declarative_base()

class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self, db_path: Optional[str] = None):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径，如果为None则使用默认路径
        """
        if db_path is None:
            # 默认数据库路径：用户文档目录下的ikonw文件夹
            home_dir = Path.home()
            self.db_dir = home_dir / "Documents" / "ikonw"
            self.db_dir.mkdir(parents=True, exist_ok=True)
            self.db_path = self.db_dir / "documents.db"
        else:
            self.db_path = Path(db_path)
            self.db_dir = self.db_path.parent
            self.db_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建SQLAlchemy引擎
        self.engine: Optional[Engine] = None
        self.SessionLocal: Optional[sessionmaker] = None
        
        logger.info(f"数据库路径设置为: {self.db_path}")
    
    def initialize(self) -> bool:
        """
        初始化数据库连接
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 创建SQLAlchemy引擎
            database_url = f"sqlite:///{self.db_path}"
            self.engine = create_engine(
                database_url,
                echo=False,  # 设置为True可以看到SQL语句
                pool_pre_ping=True,
                connect_args={"check_same_thread": False}  # SQLite特定配置
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            
            logger.info("数据库初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False
    
    def get_session(self) -> Session:
        """
        获取数据库会话
        
        Returns:
            Session: SQLAlchemy会话对象
        """
        if self.SessionLocal is None:
            raise RuntimeError("数据库未初始化，请先调用initialize()方法")
        
        return self.SessionLocal()
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")
    
    def backup_database(self, backup_path: Optional[str] = None) -> bool:
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径，如果为None则使用默认路径
            
        Returns:
            bool: 备份是否成功
        """
        try:
            if backup_path is None:
                backup_path = self.db_dir / f"documents_backup_{int(time.time())}.db"
            
            # 使用SQLite的备份API
            source = sqlite3.connect(str(self.db_path))
            backup = sqlite3.connect(str(backup_path))
            
            source.backup(backup)
            
            source.close()
            backup.close()
            
            logger.info(f"数据库备份成功: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def get_database_info(self) -> dict:
        """
        获取数据库信息
        
        Returns:
            dict: 数据库信息字典
        """
        info = {
            "database_path": str(self.db_path),
            "database_size": 0,
            "table_count": 0,
            "exists": self.db_path.exists()
        }
        
        if info["exists"]:
            info["database_size"] = self.db_path.stat().st_size
            
            # 获取表数量
            try:
                with sqlite3.connect(str(self.db_path)) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                    info["table_count"] = cursor.fetchone()[0]
            except Exception as e:
                logger.warning(f"获取数据库表信息失败: {e}")
        
        return info


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_db() -> Session:
    """
    依赖注入函数，用于获取数据库会话
    
    Returns:
        Session: 数据库会话
    """
    db = db_manager.get_session()
    try:
        yield db
    finally:
        db.close()


def init_database(db_path: Optional[str] = None) -> bool:
    """
    初始化数据库
    
    Args:
        db_path: 数据库文件路径
        
    Returns:
        bool: 初始化是否成功
    """
    global db_manager
    
    if db_path:
        db_manager = DatabaseManager(db_path)
    
    return db_manager.initialize()
