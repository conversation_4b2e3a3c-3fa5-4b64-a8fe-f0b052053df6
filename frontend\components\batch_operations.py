"""
批量操作组件
Batch Operations Component

提供批量文档处理和管理功能
"""

from typing import List, Dict, Any, Optional
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QProgressBar, QTextEdit, QCheckBox, QComboBox,
    QSpinBox, QTabWidget, QWidget, QTableWidget, QTableWidgetItem,
    QMessageBox, QFileDialog, QFrame
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer
from PySide6.QtGui import QFont, QIcon

from backend.models.document import Document


class BatchProcessingThread(QThread):
    """批量处理线程"""
    
    # 信号定义
    progress_updated = Signal(int, int, str)  # 当前进度, 总数, 状态信息
    operation_completed = Signal(int, int)    # 成功数, 总数
    error_occurred = Signal(str)              # 错误信息
    
    def __init__(self, operation_type: str, documents: List[Document], options: Dict[str, Any]):
        super().__init__()
        self.operation_type = operation_type
        self.documents = documents
        self.options = options
        self.should_stop = False
    
    def run(self):
        """运行批量操作"""
        try:
            success_count = 0
            total = len(self.documents)
            
            for i, document in enumerate(self.documents):
                if self.should_stop:
                    break
                
                # 发送进度信号
                self.progress_updated.emit(i + 1, total, f"处理文档: {document.file_name}")
                
                # 执行操作
                if self.operation_type == "delete":
                    success = self.delete_document(document)
                elif self.operation_type == "reprocess":
                    success = self.reprocess_document(document)
                elif self.operation_type == "export":
                    success = self.export_document(document)
                else:
                    success = False
                
                if success:
                    success_count += 1
                
                # 让出CPU时间
                self.msleep(100)
            
            # 发送完成信号
            self.operation_completed.emit(success_count, total)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def stop(self):
        """停止操作"""
        self.should_stop = True
    
    def delete_document(self, document: Document) -> bool:
        """删除文档"""
        # 这里应该调用后端服务删除文档
        # 暂时返回True表示成功
        return True
    
    def reprocess_document(self, document: Document) -> bool:
        """重新处理文档"""
        # 这里应该调用后端服务重新处理文档
        # 暂时返回True表示成功
        return True
    
    def export_document(self, document: Document) -> bool:
        """导出文档信息"""
        # 这里应该实现文档信息导出
        # 暂时返回True表示成功
        return True


class BatchOperationsDialog(QDialog):
    """批量操作对话框"""
    
    def __init__(self, documents: List[Document], parent=None):
        super().__init__(parent)
        
        self.documents = documents
        self.selected_documents: List[Document] = []
        self.processing_thread: Optional[BatchProcessingThread] = None
        
        # 初始化界面
        self.init_ui()
        self.init_connections()
        self.load_documents()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("批量操作")
        self.setMinimumSize(800, 600)
        self.resize(900, 700)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 文档选择标签页
        self.create_selection_tab()
        
        # 批量删除标签页
        self.create_delete_tab()
        
        # 重复文档管理标签页
        self.create_duplicate_tab()
        
        # 批量导出标签页
        self.create_export_tab()
        
        # 操作按钮
        self.create_action_buttons(layout)
    
    def create_selection_tab(self):
        """创建文档选择标签页"""
        selection_widget = QWidget()
        layout = QVBoxLayout(selection_widget)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.setMaximumWidth(80)
        toolbar_layout.addWidget(self.select_all_btn)
        
        self.select_none_btn = QPushButton("全不选")
        self.select_none_btn.setMaximumWidth(80)
        toolbar_layout.addWidget(self.select_none_btn)
        
        self.invert_selection_btn = QPushButton("反选")
        self.invert_selection_btn.setMaximumWidth(80)
        toolbar_layout.addWidget(self.invert_selection_btn)
        
        toolbar_layout.addStretch()
        
        # 过滤选项
        toolbar_layout.addWidget(QLabel("过滤:"))
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["全部", "已处理", "未处理", "有错误"])
        toolbar_layout.addWidget(self.filter_combo)
        
        layout.addLayout(toolbar_layout)
        
        # 文档列表
        self.document_list = QTableWidget()
        self.document_list.setColumnCount(5)
        self.document_list.setHorizontalHeaderLabels(["选择", "文档名", "类型", "大小", "状态"])
        self.document_list.setSelectionBehavior(QTableWidget.SelectRows)
        layout.addWidget(self.document_list)
        
        # 选择统计
        self.selection_label = QLabel("已选择: 0 个文档")
        layout.addWidget(self.selection_label)
        
        self.tab_widget.addTab(selection_widget, "文档选择")
    
    def create_delete_tab(self):
        """创建批量删除标签页"""
        delete_widget = QWidget()
        layout = QVBoxLayout(delete_widget)
        
        # 删除选项组
        options_group = QGroupBox("删除选项")
        options_layout = QVBoxLayout(options_group)
        
        self.delete_from_db_cb = QCheckBox("从数据库中删除记录")
        self.delete_from_db_cb.setChecked(True)
        options_layout.addWidget(self.delete_from_db_cb)
        
        self.delete_files_cb = QCheckBox("同时删除物理文件")
        self.delete_files_cb.setChecked(False)
        options_layout.addWidget(self.delete_files_cb)
        
        self.backup_before_delete_cb = QCheckBox("删除前备份")
        options_layout.addWidget(self.backup_before_delete_cb)
        
        layout.addWidget(options_group)
        
        # 警告信息
        warning_frame = QFrame()
        warning_frame.setFrameStyle(QFrame.StyledPanel)
        warning_frame.setStyleSheet("background-color: #fff3cd; border: 1px solid #ffeaa7;")
        warning_layout = QVBoxLayout(warning_frame)
        
        warning_label = QLabel("⚠️ 警告：删除操作不可撤销，请谨慎操作！")
        warning_label.setFont(QFont("", 10, QFont.Bold))
        warning_layout.addWidget(warning_label)
        
        layout.addWidget(warning_frame)
        
        # 删除预览
        preview_group = QGroupBox("删除预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.delete_preview_text = QTextEdit()
        self.delete_preview_text.setReadOnly(True)
        self.delete_preview_text.setMaximumHeight(200)
        preview_layout.addWidget(self.delete_preview_text)
        
        layout.addWidget(preview_group)
        layout.addStretch()
        
        self.tab_widget.addTab(delete_widget, "批量删除")
    
    def create_duplicate_tab(self):
        """创建重复文档管理标签页"""
        duplicate_widget = QWidget()
        layout = QVBoxLayout(duplicate_widget)
        
        # 检测选项组
        detection_group = QGroupBox("重复检测选项")
        detection_layout = QVBoxLayout(detection_group)
        
        self.check_hash_cb = QCheckBox("基于文件哈希检测")
        self.check_hash_cb.setChecked(True)
        detection_layout.addWidget(self.check_hash_cb)
        
        self.check_title_cb = QCheckBox("基于标题相似度检测")
        detection_layout.addWidget(self.check_title_cb)
        
        self.check_content_cb = QCheckBox("基于内容相似度检测")
        detection_layout.addWidget(self.check_content_cb)
        
        # 相似度阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("相似度阈值:"))
        self.similarity_threshold = QSpinBox()
        self.similarity_threshold.setRange(50, 100)
        self.similarity_threshold.setValue(80)
        self.similarity_threshold.setSuffix("%")
        threshold_layout.addWidget(self.similarity_threshold)
        threshold_layout.addStretch()
        detection_layout.addLayout(threshold_layout)
        
        layout.addWidget(detection_group)
        
        # 检测按钮
        detect_btn_layout = QHBoxLayout()
        self.detect_duplicates_btn = QPushButton("开始检测重复文档")
        detect_btn_layout.addWidget(self.detect_duplicates_btn)
        detect_btn_layout.addStretch()
        layout.addLayout(detect_btn_layout)
        
        # 重复文档列表
        duplicates_group = QGroupBox("重复文档")
        duplicates_layout = QVBoxLayout(duplicates_group)
        
        self.duplicates_table = QTableWidget()
        self.duplicates_table.setColumnCount(4)
        self.duplicates_table.setHorizontalHeaderLabels(["文档1", "文档2", "相似度", "操作"])
        duplicates_layout.addWidget(self.duplicates_table)
        
        layout.addWidget(duplicates_group)
        
        self.tab_widget.addTab(duplicate_widget, "重复文档")
    
    def create_export_tab(self):
        """创建批量导出标签页"""
        export_widget = QWidget()
        layout = QVBoxLayout(export_widget)
        
        # 导出选项组
        options_group = QGroupBox("导出选项")
        options_layout = QVBoxLayout(options_group)
        
        # 导出格式
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("导出格式:"))
        self.export_format_combo = QComboBox()
        self.export_format_combo.addItems(["CSV", "Excel", "JSON", "XML"])
        format_layout.addWidget(self.export_format_combo)
        format_layout.addStretch()
        options_layout.addLayout(format_layout)
        
        # 导出字段
        fields_label = QLabel("导出字段:")
        options_layout.addWidget(fields_label)
        
        self.export_fields = {}
        fields = [
            ("文件名", "file_name"),
            ("标题", "title"),
            ("作者", "author"),
            ("文档类型", "document_type"),
            ("文件大小", "file_size"),
            ("页数", "page_count"),
            ("字数", "word_count"),
            ("创建时间", "created_at"),
            ("修改时间", "updated_at")
        ]
        
        for field_name, field_key in fields:
            cb = QCheckBox(field_name)
            cb.setChecked(True)
            self.export_fields[field_key] = cb
            options_layout.addWidget(cb)
        
        layout.addWidget(options_group)
        
        # 导出路径
        path_group = QGroupBox("导出路径")
        path_layout = QVBoxLayout(path_group)
        
        path_select_layout = QHBoxLayout()
        self.export_path_label = QLabel("未选择导出路径")
        path_select_layout.addWidget(self.export_path_label)
        
        self.select_export_path_btn = QPushButton("选择路径")
        path_select_layout.addWidget(self.select_export_path_btn)
        
        path_layout.addLayout(path_select_layout)
        layout.addWidget(path_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(export_widget, "批量导出")
    
    def create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        button_layout.addWidget(self.progress_bar)
        
        button_layout.addStretch()
        
        # 执行按钮
        self.execute_btn = QPushButton("执行操作")
        self.execute_btn.setMinimumWidth(100)
        button_layout.addWidget(self.execute_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setMinimumWidth(100)
        button_layout.addWidget(self.cancel_btn)
        
        parent_layout.addLayout(button_layout)
    
    def init_connections(self):
        """初始化信号连接"""
        # 文档选择
        self.select_all_btn.clicked.connect(self.select_all_documents)
        self.select_none_btn.clicked.connect(self.select_no_documents)
        self.invert_selection_btn.clicked.connect(self.invert_selection)
        self.filter_combo.currentTextChanged.connect(self.filter_documents)
        
        # 重复检测
        self.detect_duplicates_btn.clicked.connect(self.detect_duplicates)
        
        # 导出
        self.select_export_path_btn.clicked.connect(self.select_export_path)
        
        # 操作按钮
        self.execute_btn.clicked.connect(self.execute_operation)
        self.cancel_btn.clicked.connect(self.cancel_operation)
    
    def load_documents(self):
        """加载文档列表"""
        self.document_list.setRowCount(len(self.documents))
        
        for row, document in enumerate(self.documents):
            # 选择框
            checkbox = QCheckBox()
            self.document_list.setCellWidget(row, 0, checkbox)
            
            # 文档名
            name_item = QTableWidgetItem(document.file_name)
            self.document_list.setItem(row, 1, name_item)
            
            # 类型
            type_item = QTableWidgetItem(document.document_type or document.detected_type)
            self.document_list.setItem(row, 2, type_item)
            
            # 大小
            size_item = QTableWidgetItem(self.format_file_size(document.file_size))
            self.document_list.setItem(row, 3, size_item)
            
            # 状态
            status = "已处理" if document.is_processed else "未处理"
            if document.processing_error:
                status = "有错误"
            status_item = QTableWidgetItem(status)
            self.document_list.setItem(row, 4, status_item)
        
        self.update_selection_count()
    
    def format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def get_selected_documents(self) -> List[Document]:
        """获取选中的文档"""
        selected = []
        for row in range(self.document_list.rowCount()):
            checkbox = self.document_list.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected.append(self.documents[row])
        return selected
    
    def update_selection_count(self):
        """更新选择计数"""
        selected = self.get_selected_documents()
        self.selection_label.setText(f"已选择: {len(selected)} 个文档")
        self.selected_documents = selected
    
    def select_all_documents(self):
        """全选文档"""
        for row in range(self.document_list.rowCount()):
            checkbox = self.document_list.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)
        self.update_selection_count()
    
    def select_no_documents(self):
        """全不选文档"""
        for row in range(self.document_list.rowCount()):
            checkbox = self.document_list.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)
        self.update_selection_count()
    
    def invert_selection(self):
        """反选文档"""
        for row in range(self.document_list.rowCount()):
            checkbox = self.document_list.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(not checkbox.isChecked())
        self.update_selection_count()
    
    def filter_documents(self):
        """过滤文档显示"""
        filter_type = self.filter_combo.currentText()
        
        for row in range(self.document_list.rowCount()):
            document = self.documents[row]
            show_row = True
            
            if filter_type == "已处理":
                show_row = document.is_processed
            elif filter_type == "未处理":
                show_row = not document.is_processed
            elif filter_type == "有错误":
                show_row = bool(document.processing_error)
            
            self.document_list.setRowHidden(row, not show_row)
    
    def detect_duplicates(self):
        """检测重复文档"""
        QMessageBox.information(self, "提示", "重复文档检测功能开发中...")
    
    def select_export_path(self):
        """选择导出路径"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.Directory)
        
        if file_dialog.exec():
            path = file_dialog.selectedFiles()[0]
            self.export_path_label.setText(path)
    
    def execute_operation(self):
        """执行操作"""
        current_tab = self.tab_widget.currentIndex()
        selected_docs = self.get_selected_documents()
        
        if not selected_docs:
            QMessageBox.warning(self, "警告", "请先选择要操作的文档")
            return
        
        if current_tab == 1:  # 批量删除
            self.execute_delete_operation(selected_docs)
        elif current_tab == 3:  # 批量导出
            self.execute_export_operation(selected_docs)
        else:
            QMessageBox.information(self, "提示", "请选择要执行的操作")
    
    def execute_delete_operation(self, documents: List[Document]):
        """执行删除操作"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除选中的 {len(documents)} 个文档吗？\n\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.start_batch_operation("delete", documents, {})
    
    def execute_export_operation(self, documents: List[Document]):
        """执行导出操作"""
        if self.export_path_label.text() == "未选择导出路径":
            QMessageBox.warning(self, "警告", "请先选择导出路径")
            return
        
        # 获取导出选项
        options = {
            "format": self.export_format_combo.currentText(),
            "path": self.export_path_label.text(),
            "fields": {key: cb.isChecked() for key, cb in self.export_fields.items()}
        }
        
        self.start_batch_operation("export", documents, options)
    
    def start_batch_operation(self, operation_type: str, documents: List[Document], options: Dict[str, Any]):
        """开始批量操作"""
        if self.processing_thread and self.processing_thread.isRunning():
            QMessageBox.warning(self, "警告", "已有操作正在进行中")
            return
        
        # 创建处理线程
        self.processing_thread = BatchProcessingThread(operation_type, documents, options)
        
        # 连接信号
        self.processing_thread.progress_updated.connect(self.on_progress_updated)
        self.processing_thread.operation_completed.connect(self.on_operation_completed)
        self.processing_thread.error_occurred.connect(self.on_error_occurred)
        
        # 更新界面
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(documents))
        self.progress_bar.setValue(0)
        self.execute_btn.setEnabled(False)
        
        # 启动线程
        self.processing_thread.start()
    
    def cancel_operation(self):
        """取消操作"""
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.stop()
            self.processing_thread.wait(3000)
        
        self.close()
    
    def on_progress_updated(self, current: int, total: int, status: str):
        """进度更新事件"""
        self.progress_bar.setValue(current)
        self.progress_bar.setFormat(f"{status} ({current}/{total})")
    
    def on_operation_completed(self, success_count: int, total_count: int):
        """操作完成事件"""
        self.progress_bar.setVisible(False)
        self.execute_btn.setEnabled(True)
        
        QMessageBox.information(
            self, "操作完成",
            f"批量操作完成！\n\n成功: {success_count}\n总数: {total_count}"
        )
    
    def on_error_occurred(self, error_message: str):
        """错误发生事件"""
        self.progress_bar.setVisible(False)
        self.execute_btn.setEnabled(True)
        
        QMessageBox.critical(self, "错误", f"操作过程中发生错误:\n{error_message}")
