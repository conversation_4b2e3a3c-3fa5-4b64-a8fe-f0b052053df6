"""
文件类型检测器测试
File Type Detector Tests
"""

import pytest
import tempfile
from pathlib import Path
from backend.utils.file_detector import FileTypeDetector


class TestFileTypeDetector:
    """文件类型检测器测试类"""
    
    def setup_method(self):
        """测试前的设置"""
        self.detector = FileTypeDetector()
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def teardown_method(self):
        """测试后的清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_file(self, filename: str, content: bytes) -> Path:
        """创建测试文件"""
        file_path = self.temp_dir / filename
        with open(file_path, 'wb') as f:
            f.write(content)
        return file_path
    
    def test_pdf_detection(self):
        """测试PDF文件检测"""
        # 创建一个带有PDF文件头的测试文件
        pdf_content = b'%PDF-1.4\n%\xe2\xe3\xcf\xd3\n'
        pdf_file = self.create_test_file('test.pdf', pdf_content)
        
        file_type, method, confidence = self.detector.detect_file_type(pdf_file)
        assert file_type == 'pdf'
        assert method == 'signature'
        assert confidence == 0.9
    
    def test_text_file_detection(self):
        """测试文本文件检测"""
        # 创建文本文件
        text_content = b'This is a test text file.\nHello World!'
        txt_file = self.create_test_file('test.txt', text_content)
        
        file_type, method, confidence = self.detector.detect_file_type(txt_file)
        assert file_type == 'txt'
        assert confidence > 0.0
    
    def test_markdown_detection(self):
        """测试Markdown文件检测"""
        md_content = b'# Test Markdown\n\nThis is a **test** markdown file.'
        md_file = self.create_test_file('test.md', md_content)
        
        file_type, method, confidence = self.detector.detect_file_type(md_file)
        assert file_type == 'md'
        assert confidence > 0.0
    
    def test_unknown_file_detection(self):
        """测试未知文件类型检测"""
        unknown_content = b'\x00\x01\x02\x03\x04\x05'
        unknown_file = self.create_test_file('test.unknown', unknown_content)
        
        file_type, method, confidence = self.detector.detect_file_type(unknown_file)
        assert file_type == 'unknown'
    
    def test_is_document_file(self):
        """测试文档文件判断"""
        # PDF文件
        pdf_content = b'%PDF-1.4\n'
        pdf_file = self.create_test_file('test.pdf', pdf_content)
        assert self.detector.is_document_file(pdf_file) == True
        
        # 文本文件
        text_content = b'Hello World!'
        txt_file = self.create_test_file('test.txt', text_content)
        assert self.detector.is_document_file(txt_file) == True
        
        # 非文档文件
        binary_content = b'\x00\x01\x02\x03'
        bin_file = self.create_test_file('test.bin', binary_content)
        assert self.detector.is_document_file(bin_file) == False
    
    def test_get_file_info(self):
        """测试获取文件信息"""
        text_content = b'Test content'
        txt_file = self.create_test_file('test.txt', text_content)
        
        info = self.detector.get_file_info(txt_file)
        
        assert info['file_name'] == 'test.txt'
        assert info['file_extension'] == 'txt'
        assert info['detected_type'] == 'txt'
        assert info['is_document'] == True
        assert info['file_size'] == len(text_content)
        assert 'file_path' in info
        assert 'confidence' in info
    
    def test_nonexistent_file(self):
        """测试不存在的文件"""
        nonexistent_file = self.temp_dir / 'nonexistent.txt'
        
        file_type, method, confidence = self.detector.detect_file_type(nonexistent_file)
        assert file_type == 'unknown'
        assert method == 'file_not_found'
        assert confidence == 0.0


if __name__ == '__main__':
    pytest.main([__file__])
