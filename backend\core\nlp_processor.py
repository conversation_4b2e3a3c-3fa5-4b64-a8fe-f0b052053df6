"""
自然语言处理器
Natural Language Processor

实现关键词提取、摘要生成、文本分析等NLP功能
"""

import re
import math
from collections import Counter, defaultdict
from typing import List, Dict, Tuple, Optional

try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class SimpleNLPProcessor:
    """简化版NLP处理器（不依赖外部库）"""
    
    def __init__(self):
        # 中文停用词
        self.chinese_stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '里', '就是', '还', '把', '比', '或者', '什么', '可以',
            '这个', '那个', '这些', '那些', '怎么', '为什么', '因为', '所以', '但是',
            '然后', '如果', '虽然', '虽说', '尽管', '无论', '不管', '除了', '除非',
            '只要', '只有', '不仅', '而且', '并且', '以及', '还有', '另外', '此外',
            '总之', '因此', '所以', '于是', '然而', '不过', '可是', '只是', '仅仅'
        }
        
        # 英文停用词
        self.english_stopwords = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'would', 'you', 'your', 'have', 'had',
            'this', 'these', 'they', 'were', 'been', 'their', 'said', 'each',
            'which', 'she', 'do', 'how', 'if', 'we', 'when', 'where', 'who',
            'why', 'what', 'can', 'could', 'should', 'would', 'may', 'might',
            'must', 'shall', 'will', 'am', 'are', 'is', 'was', 'were', 'being',
            'been', 'have', 'has', 'had', 'do', 'does', 'did', 'doing', 'done'
        }
        
        logger.info("简化版NLP处理器初始化完成")
    
    def extract_keywords(self, text: str, max_keywords: int = 20) -> List[Tuple[str, int]]:
        """
        提取关键词
        
        Args:
            text: 输入文本
            max_keywords: 最大关键词数量
            
        Returns:
            List[Tuple[str, int]]: 关键词和频次的列表
        """
        if not text:
            return []
        
        # 提取中文词汇
        chinese_words = self._extract_chinese_words(text)
        
        # 提取英文词汇
        english_words = self._extract_english_words(text)
        
        # 合并词汇并统计频次
        all_words = chinese_words + english_words
        word_freq = Counter(all_words)
        
        # 过滤停用词和短词
        filtered_words = {}
        for word, freq in word_freq.items():
            if self._is_valid_keyword(word):
                filtered_words[word] = freq
        
        # 按频次排序
        sorted_keywords = sorted(filtered_words.items(), key=lambda x: x[1], reverse=True)
        
        return sorted_keywords[:max_keywords]
    
    def _extract_chinese_words(self, text: str) -> List[str]:
        """提取中文词汇"""
        # 简单的中文分词（基于标点符号和空格）
        chinese_text = re.sub(r'[^\u4e00-\u9fff]', ' ', text)
        
        # 提取2-4字的中文词组
        words = []
        for length in [4, 3, 2]:  # 优先提取长词
            pattern = f'[\u4e00-\u9fff]{{{length}}}'
            matches = re.findall(pattern, chinese_text)
            words.extend(matches)
        
        return words
    
    def _extract_english_words(self, text: str) -> List[str]:
        """提取英文词汇"""
        # 提取英文单词
        english_words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        return english_words
    
    def _is_valid_keyword(self, word: str) -> bool:
        """判断是否为有效关键词"""
        # 长度检查
        if len(word) < 2:
            return False
        
        # 停用词检查
        if word.lower() in self.chinese_stopwords or word.lower() in self.english_stopwords:
            return False
        
        # 数字检查
        if word.isdigit():
            return False
        
        # 特殊字符检查
        if re.match(r'^[^\w\u4e00-\u9fff]+$', word):
            return False
        
        return True
    
    def generate_summary(self, text: str, max_sentences: int = 3) -> str:
        """
        生成文档摘要
        
        Args:
            text: 输入文本
            max_sentences: 最大句子数
            
        Returns:
            str: 生成的摘要
        """
        if not text:
            return ""
        
        # 分句
        sentences = self._split_sentences(text)
        
        if len(sentences) <= max_sentences:
            return text
        
        # 计算句子重要性分数
        sentence_scores = self._calculate_sentence_scores(sentences)
        
        # 选择得分最高的句子
        top_sentences = sorted(sentence_scores.items(), key=lambda x: x[1], reverse=True)
        selected_sentences = [sent for sent, score in top_sentences[:max_sentences]]
        
        # 按原文顺序排列
        summary_sentences = []
        for sentence in sentences:
            if sentence in selected_sentences:
                summary_sentences.append(sentence)
        
        return ''.join(summary_sentences)
    
    def _split_sentences(self, text: str) -> List[str]:
        """分句"""
        # 中英文句子分隔符
        sentence_endings = r'[。！？.!?]+'
        sentences = re.split(sentence_endings, text)
        
        # 清理空句子和过短句子
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:  # 过滤过短的句子
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
    
    def _calculate_sentence_scores(self, sentences: List[str]) -> Dict[str, float]:
        """计算句子重要性分数"""
        scores = {}
        
        # 计算词频
        all_words = []
        for sentence in sentences:
            words = self._extract_chinese_words(sentence) + self._extract_english_words(sentence)
            all_words.extend(words)
        
        word_freq = Counter(all_words)
        
        # 计算每个句子的分数
        for sentence in sentences:
            words = self._extract_chinese_words(sentence) + self._extract_english_words(sentence)
            
            if not words:
                scores[sentence] = 0.0
                continue
            
            # 基于词频的分数
            word_score = sum(word_freq.get(word, 0) for word in words if self._is_valid_keyword(word))
            
            # 句子长度权重
            length_score = len(sentence) / 100  # 适中长度的句子得分更高
            if length_score > 1:
                length_score = 1 / length_score
            
            # 位置权重（开头和结尾的句子更重要）
            position = sentences.index(sentence)
            total_sentences = len(sentences)
            if position < total_sentences * 0.3 or position > total_sentences * 0.7:
                position_score = 1.2
            else:
                position_score = 1.0
            
            scores[sentence] = word_score * length_score * position_score
        
        return scores
    
    def detect_language_detailed(self, text: str) -> Dict[str, float]:
        """
        详细的语言检测
        
        Args:
            text: 输入文本
            
        Returns:
            Dict[str, float]: 各语言的比例
        """
        if not text:
            return {'unknown': 1.0}
        
        # 统计各种字符
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        numbers = len(re.findall(r'\d', text))
        punctuation = len(re.findall(r'[^\w\s\u4e00-\u9fff]', text))
        
        total_chars = len(text)
        
        if total_chars == 0:
            return {'unknown': 1.0}
        
        result = {
            'chinese': chinese_chars / total_chars,
            'english': english_chars / total_chars,
            'numbers': numbers / total_chars,
            'punctuation': punctuation / total_chars
        }
        
        return result
    
    def extract_named_entities(self, text: str) -> Dict[str, List[str]]:
        """
        简单的命名实体识别
        
        Args:
            text: 输入文本
            
        Returns:
            Dict[str, List[str]]: 实体类型和实体列表
        """
        entities = {
            'dates': [],
            'numbers': [],
            'organizations': [],
            'locations': [],
            'persons': []
        }
        
        # 日期识别
        date_patterns = [
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{4}-\d{1,2}-\d{1,2}',
            r'\d{4}/\d{1,2}/\d{1,2}',
            r'\d{4}\.\d{1,2}\.\d{1,2}'
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            entities['dates'].extend(matches)
        
        # 数字识别
        number_patterns = [
            r'\d+\.\d+%',  # 百分比
            r'\d+万',      # 万
            r'\d+亿',      # 亿
            r'\d+千',      # 千
        ]
        
        for pattern in number_patterns:
            matches = re.findall(pattern, text)
            entities['numbers'].extend(matches)
        
        # 机构识别（简单模式）
        org_patterns = [
            r'[\u4e00-\u9fff]+大学',
            r'[\u4e00-\u9fff]+公司',
            r'[\u4e00-\u9fff]+学院',
            r'[\u4e00-\u9fff]+研究所',
            r'[\u4e00-\u9fff]+出版社'
        ]
        
        for pattern in org_patterns:
            matches = re.findall(pattern, text)
            entities['organizations'].extend(matches)
        
        return entities


class AdvancedNLPProcessor(SimpleNLPProcessor):
    """高级NLP处理器（使用外部库）"""
    
    def __init__(self):
        super().__init__()
        self.jieba_available = False
        self.nltk_available = False
        
        try:
            import jieba
            self.jieba = jieba
            self.jieba_available = True
            logger.info("jieba中文分词库加载成功")
        except ImportError:
            logger.warning("jieba库未安装，将使用简化版中文处理")
        
        try:
            import nltk
            self.nltk = nltk
            self.nltk_available = True
            logger.info("NLTK库加载成功")
        except ImportError:
            logger.warning("NLTK库未安装，将使用简化版英文处理")
    
    def extract_keywords_advanced(self, text: str, max_keywords: int = 20) -> List[Tuple[str, int]]:
        """使用高级算法提取关键词"""
        if self.jieba_available:
            return self._extract_keywords_with_jieba(text, max_keywords)
        else:
            return self.extract_keywords(text, max_keywords)
    
    def _extract_keywords_with_jieba(self, text: str, max_keywords: int) -> List[Tuple[str, int]]:
        """使用jieba进行中文关键词提取"""
        try:
            import jieba.analyse
            
            # 使用TF-IDF算法提取关键词
            keywords = jieba.analyse.extract_tags(text, topK=max_keywords, withWeight=True)
            
            # 转换为整数频次（近似）
            result = []
            for word, weight in keywords:
                freq = int(weight * 100)  # 将权重转换为近似频次
                result.append((word, freq))
            
            return result
            
        except Exception as e:
            logger.warning(f"jieba关键词提取失败: {e}")
            return self.extract_keywords(text, max_keywords)


# 根据可用库选择处理器
def create_nlp_processor():
    """创建NLP处理器实例"""
    try:
        return AdvancedNLPProcessor()
    except Exception:
        return SimpleNLPProcessor()


# 全局NLP处理器实例
nlp_processor = create_nlp_processor()
