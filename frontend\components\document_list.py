"""
文档列表组件
Document List Component

提供高级的文档列表展示功能
"""

from typing import List, Optional, Dict, Any
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QMenu, QMessageBox, QPushButton,
    QLabel, QComboBox, QCheckBox, QSpinBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QAction, QFont, QColor

from backend.models.document import Document


class DocumentListWidget(QTableWidget):
    """增强的文档列表表格组件"""
    
    # 信号定义
    document_selected = Signal(Document)
    document_double_clicked = Signal(Document)
    documents_selection_changed = Signal(list)  # 多选文档
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 数据
        self.documents: List[Document] = []
        self.filtered_documents: List[Document] = []
        
        # 设置表格
        self.setup_table()
        self.setup_context_menu()
        
        # 连接信号
        self.itemSelectionChanged.connect(self.on_selection_changed)
        self.itemDoubleClicked.connect(self.on_item_double_clicked)
    
    def setup_table(self):
        """设置表格属性"""
        # 列定义
        self.columns = [
            {"name": "标题", "field": "title", "width": 300},
            {"name": "作者", "field": "author", "width": 150},
            {"name": "类型", "field": "document_type", "width": 80},
            {"name": "语言", "field": "language", "width": 60},
            {"name": "大小", "field": "file_size", "width": 80},
            {"name": "页数", "field": "page_count", "width": 60},
            {"name": "字数", "field": "word_count", "width": 80},
            {"name": "修改时间", "field": "updated_at", "width": 120},
            {"name": "状态", "field": "is_processed", "width": 60}
        ]
        
        # 设置列
        self.setColumnCount(len(self.columns))
        headers = [col["name"] for col in self.columns]
        self.setHorizontalHeaderLabels(headers)
        
        # 设置列宽
        for i, col in enumerate(self.columns):
            self.setColumnWidth(i, col["width"])
        
        # 表格属性
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)  # 支持多选
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        self.setShowGrid(True)
        
        # 表头属性
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 标题列自适应
        
        # 垂直表头
        self.verticalHeader().setVisible(False)
        
        # 样式
        self.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #ddd;
                selection-background-color: #e3f2fd;
                gridline-color: #f0f0f0;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QTableWidget::item:hover {
                background-color: #f5f5f5;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
    
    def setup_context_menu(self):
        """设置右键菜单"""
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if not self.itemAt(position):
            return
        
        menu = QMenu(self)
        
        # 打开文档
        open_action = QAction("打开文档", self)
        open_action.triggered.connect(self.open_selected_document)
        menu.addAction(open_action)
        
        # 查看详情
        detail_action = QAction("查看详情", self)
        detail_action.triggered.connect(self.show_document_details)
        menu.addAction(detail_action)
        
        menu.addSeparator()
        
        # 复制路径
        copy_path_action = QAction("复制文件路径", self)
        copy_path_action.triggered.connect(self.copy_file_path)
        menu.addAction(copy_path_action)
        
        # 在文件管理器中显示
        show_in_explorer_action = QAction("在文件管理器中显示", self)
        show_in_explorer_action.triggered.connect(self.show_in_explorer)
        menu.addAction(show_in_explorer_action)
        
        menu.addSeparator()
        
        # 删除文档
        delete_action = QAction("删除文档", self)
        delete_action.triggered.connect(self.delete_selected_documents)
        menu.addAction(delete_action)
        
        # 显示菜单
        menu.exec(self.mapToGlobal(position))
    
    def load_documents(self, documents: List[Document]):
        """加载文档列表"""
        self.documents = documents
        self.filtered_documents = documents.copy()
        self.update_table()
    
    def update_table(self):
        """更新表格显示"""
        self.setRowCount(len(self.filtered_documents))
        
        for row, document in enumerate(self.filtered_documents):
            self.populate_row(row, document)
        
        # 调整行高
        self.resizeRowsToContents()
    
    def populate_row(self, row: int, document: Document):
        """填充表格行数据"""
        # 标题
        title = document.title or document.file_name
        title_item = QTableWidgetItem(title)
        title_item.setToolTip(title)
        self.setItem(row, 0, title_item)
        
        # 作者
        author = document.author or "未知"
        author_item = QTableWidgetItem(author)
        self.setItem(row, 1, author_item)
        
        # 类型
        doc_type = document.document_type or document.detected_type
        type_item = QTableWidgetItem(doc_type)
        self.setItem(row, 2, type_item)
        
        # 语言
        language = self.format_language(document.language)
        lang_item = QTableWidgetItem(language)
        self.setItem(row, 3, lang_item)
        
        # 大小
        size = self.format_file_size(document.file_size)
        size_item = QTableWidgetItem(size)
        size_item.setData(Qt.UserRole, document.file_size)  # 存储原始值用于排序
        self.setItem(row, 4, size_item)
        
        # 页数
        pages = str(document.page_count) if document.page_count else "-"
        pages_item = QTableWidgetItem(pages)
        if document.page_count:
            pages_item.setData(Qt.UserRole, document.page_count)
        self.setItem(row, 5, pages_item)
        
        # 字数
        words = str(document.word_count) if document.word_count else "-"
        words_item = QTableWidgetItem(words)
        if document.word_count:
            words_item.setData(Qt.UserRole, document.word_count)
        self.setItem(row, 6, words_item)
        
        # 修改时间
        time_str = document.updated_at.strftime('%Y-%m-%d %H:%M') if document.updated_at else "未知"
        time_item = QTableWidgetItem(time_str)
        if document.updated_at:
            time_item.setData(Qt.UserRole, document.updated_at)
        self.setItem(row, 7, time_item)
        
        # 状态
        status = "已处理" if document.is_processed else "未处理"
        status_item = QTableWidgetItem(status)
        if document.is_processed:
            status_item.setForeground(QColor("#4caf50"))
        else:
            status_item.setForeground(QColor("#f44336"))
        self.setItem(row, 8, status_item)
    
    def format_language(self, language: str) -> str:
        """格式化语言显示"""
        if not language:
            return "未知"
        
        lang_map = {
            "zh": "中文",
            "en": "英文",
            "mixed": "中英混合"
        }
        return lang_map.get(language, language)
    
    def format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def on_selection_changed(self):
        """选择变化事件"""
        selected_rows = set()
        for item in self.selectedItems():
            selected_rows.add(item.row())
        
        selected_documents = []
        for row in selected_rows:
            if row < len(self.filtered_documents):
                selected_documents.append(self.filtered_documents[row])
        
        # 发送信号
        self.documents_selection_changed.emit(selected_documents)
        
        # 如果只选择了一个文档，发送单选信号
        if len(selected_documents) == 1:
            self.document_selected.emit(selected_documents[0])
    
    def on_item_double_clicked(self, item):
        """双击事件"""
        row = item.row()
        if row < len(self.filtered_documents):
            document = self.filtered_documents[row]
            self.document_double_clicked.emit(document)
    
    def get_selected_documents(self) -> List[Document]:
        """获取选中的文档"""
        selected_rows = set()
        for item in self.selectedItems():
            selected_rows.add(item.row())
        
        return [self.filtered_documents[row] for row in selected_rows 
                if row < len(self.filtered_documents)]
    
    def filter_documents(self, filter_func):
        """过滤文档"""
        self.filtered_documents = [doc for doc in self.documents if filter_func(doc)]
        self.update_table()
    
    def search_documents(self, keyword: str):
        """搜索文档"""
        if not keyword:
            self.filtered_documents = self.documents.copy()
        else:
            keyword = keyword.lower()
            self.filtered_documents = []
            for doc in self.documents:
                if (keyword in (doc.title or "").lower() or
                    keyword in (doc.author or "").lower() or
                    keyword in (doc.file_name or "").lower() or
                    keyword in (doc.content_preview or "").lower()):
                    self.filtered_documents.append(doc)
        
        self.update_table()
    
    # === 右键菜单动作 ===
    
    def open_selected_document(self):
        """打开选中的文档"""
        selected = self.get_selected_documents()
        if selected:
            # 这里应该调用系统默认程序打开文档
            import os
            import subprocess
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(selected[0].file_path)
                elif os.name == 'posix':  # macOS and Linux
                    subprocess.call(['open', selected[0].file_path])
            except Exception as e:
                QMessageBox.warning(self, "错误", f"无法打开文档: {str(e)}")
    
    def show_document_details(self):
        """显示文档详情"""
        selected = self.get_selected_documents()
        if selected:
            # 这里应该打开详情对话框
            QMessageBox.information(self, "文档详情", f"文档: {selected[0].title or selected[0].file_name}")
    
    def copy_file_path(self):
        """复制文件路径"""
        selected = self.get_selected_documents()
        if selected:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(selected[0].file_path)
    
    def show_in_explorer(self):
        """在文件管理器中显示"""
        selected = self.get_selected_documents()
        if selected:
            import os
            import subprocess
            try:
                if os.name == 'nt':  # Windows
                    subprocess.run(['explorer', '/select,', selected[0].file_path])
                elif os.name == 'posix':  # macOS and Linux
                    subprocess.run(['open', '-R', selected[0].file_path])
            except Exception as e:
                QMessageBox.warning(self, "错误", f"无法在文件管理器中显示: {str(e)}")
    
    def delete_selected_documents(self):
        """删除选中的文档"""
        selected = self.get_selected_documents()
        if not selected:
            return
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除选中的 {len(selected)} 个文档吗？\n\n注意：这只会从数据库中删除记录，不会删除实际文件。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 这里应该调用后端服务删除文档
            QMessageBox.information(self, "提示", "删除功能开发中...")
