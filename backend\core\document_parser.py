"""
文档内容解析器
Document Content Parser

解析各种格式的文档内容，提取文本、元数据等信息
"""

import re
import os
import zipfile
from pathlib import Path
from typing import Dict, Optional, List, Tuple
from abc import ABC, abstractmethod

try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class DocumentParseResult:
    """文档解析结果类"""
    
    def __init__(self):
        self.success: bool = False
        self.content: str = ""
        self.metadata: Dict = {}
        self.error_message: str = ""
        self.page_count: int = 0
        self.word_count: int = 0
        self.language: str = "unknown"
        self.encoding: str = "utf-8"
    
    def calculate_word_count(self):
        """计算字数"""
        if self.content:
            # 简单的字数统计（中英文混合）
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', self.content))
            english_words = len(re.findall(r'\b[a-zA-Z]+\b', self.content))
            self.word_count = chinese_chars + english_words
        else:
            self.word_count = 0


class BaseDocumentParser(ABC):
    """文档解析器基类"""
    
    @abstractmethod
    def can_parse(self, file_path: Path) -> bool:
        """判断是否可以解析该文件"""
        pass
    
    @abstractmethod
    def parse(self, file_path: Path) -> DocumentParseResult:
        """解析文档"""
        pass


class TextDocumentParser(BaseDocumentParser):
    """文本文档解析器（TXT、MD等）"""
    
    SUPPORTED_EXTENSIONS = {'.txt', '.md', '.markdown', '.rst', '.log'}
    
    def can_parse(self, file_path: Path) -> bool:
        return file_path.suffix.lower() in self.SUPPORTED_EXTENSIONS
    
    def detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'ascii']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read()
                return encoding
            except (UnicodeDecodeError, UnicodeError):
                continue
        
        return 'utf-8'  # 默认编码
    
    def parse(self, file_path: Path) -> DocumentParseResult:
        result = DocumentParseResult()
        
        try:
            # 检测编码
            encoding = self.detect_encoding(file_path)
            result.encoding = encoding
            
            # 读取文件内容
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                content = f.read()
            
            result.content = content
            result.success = True
            
            # 计算页数（按每页500字估算）
            result.page_count = max(1, len(content) // 500)
            
            # 计算字数
            result.calculate_word_count()
            
            # 检测语言
            result.language = self._detect_language(content)
            
            # 提取元数据
            result.metadata = self._extract_metadata(content, file_path)
            
        except Exception as e:
            result.error_message = f"解析文本文档失败: {str(e)}"
            logger.error(result.error_message)
        
        return result
    
    def _detect_language(self, content: str) -> str:
        """简单的语言检测"""
        if not content:
            return "unknown"
        
        # 统计中文字符
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', content))
        # 统计英文字符
        english_chars = len(re.findall(r'[a-zA-Z]', content))
        
        total_chars = chinese_chars + english_chars
        if total_chars == 0:
            return "unknown"
        
        chinese_ratio = chinese_chars / total_chars
        
        if chinese_ratio > 0.3:
            return "zh"  # 中文
        elif english_chars > chinese_chars:
            return "en"  # 英文
        else:
            return "mixed"  # 混合
    
    def _extract_metadata(self, content: str, file_path: Path) -> Dict:
        """提取文本文档元数据"""
        metadata = {}
        
        # 对于Markdown文件，尝试提取标题
        if file_path.suffix.lower() in ['.md', '.markdown']:
            lines = content.split('\n')
            for line in lines[:10]:  # 只检查前10行
                line = line.strip()
                if line.startswith('# '):
                    metadata['title'] = line[2:].strip()
                    break
        
        # 尝试从文件名提取标题
        if 'title' not in metadata:
            metadata['title'] = file_path.stem
        
        return metadata


class PDFDocumentParser(BaseDocumentParser):
    """PDF文档解析器"""
    
    def can_parse(self, file_path: Path) -> bool:
        return file_path.suffix.lower() == '.pdf'
    
    def parse(self, file_path: Path) -> DocumentParseResult:
        result = DocumentParseResult()
        
        try:
            # 尝试使用PyPDF2解析
            import PyPDF2
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # 获取页数
                result.page_count = len(pdf_reader.pages)
                
                # 提取文本内容
                content_parts = []
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    if text.strip():
                        content_parts.append(text)
                
                result.content = '\n'.join(content_parts)
                result.success = True
                
                # 计算字数
                result.calculate_word_count()
                
                # 检测语言
                result.language = self._detect_language(result.content)
                
                # 提取元数据
                result.metadata = self._extract_pdf_metadata(pdf_reader)
                
        except ImportError:
            result.error_message = "PyPDF2库未安装，无法解析PDF文件"
            logger.warning(result.error_message)
        except Exception as e:
            result.error_message = f"解析PDF文档失败: {str(e)}"
            logger.error(result.error_message)
        
        return result
    
    def _detect_language(self, content: str) -> str:
        """检测语言（复用文本解析器的方法）"""
        parser = TextDocumentParser()
        return parser._detect_language(content)
    
    def _extract_pdf_metadata(self, pdf_reader) -> Dict:
        """提取PDF元数据"""
        metadata = {}
        
        try:
            if pdf_reader.metadata:
                pdf_meta = pdf_reader.metadata
                
                if '/Title' in pdf_meta:
                    metadata['title'] = str(pdf_meta['/Title'])
                if '/Author' in pdf_meta:
                    metadata['author'] = str(pdf_meta['/Author'])
                if '/Subject' in pdf_meta:
                    metadata['subject'] = str(pdf_meta['/Subject'])
                if '/Creator' in pdf_meta:
                    metadata['creator'] = str(pdf_meta['/Creator'])
                if '/Producer' in pdf_meta:
                    metadata['producer'] = str(pdf_meta['/Producer'])
                if '/CreationDate' in pdf_meta:
                    metadata['creation_date'] = str(pdf_meta['/CreationDate'])
                if '/ModDate' in pdf_meta:
                    metadata['modification_date'] = str(pdf_meta['/ModDate'])
        
        except Exception as e:
            logger.warning(f"提取PDF元数据失败: {e}")
        
        return metadata


class WordDocumentParser(BaseDocumentParser):
    """Word文档解析器（DOC、DOCX）"""
    
    def can_parse(self, file_path: Path) -> bool:
        return file_path.suffix.lower() in ['.docx', '.doc']
    
    def parse(self, file_path: Path) -> DocumentParseResult:
        result = DocumentParseResult()
        
        if file_path.suffix.lower() == '.docx':
            return self._parse_docx(file_path)
        elif file_path.suffix.lower() == '.doc':
            return self._parse_doc(file_path)
        
        result.error_message = "不支持的Word文档格式"
        return result
    
    def _parse_docx(self, file_path: Path) -> DocumentParseResult:
        """解析DOCX文档"""
        result = DocumentParseResult()
        
        try:
            import docx
            
            doc = docx.Document(file_path)
            
            # 提取文本内容
            content_parts = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_parts.append(paragraph.text)
            
            result.content = '\n'.join(content_parts)
            result.success = True
            
            # 估算页数（按每页500字）
            result.page_count = max(1, len(result.content) // 500)
            
            # 计算字数
            result.calculate_word_count()
            
            # 检测语言
            result.language = self._detect_language(result.content)
            
            # 提取元数据
            result.metadata = self._extract_docx_metadata(doc)
            
        except ImportError:
            result.error_message = "python-docx库未安装，无法解析DOCX文件"
            logger.warning(result.error_message)
        except Exception as e:
            result.error_message = f"解析DOCX文档失败: {str(e)}"
            logger.error(result.error_message)
        
        return result
    
    def _parse_doc(self, file_path: Path) -> DocumentParseResult:
        """解析DOC文档（旧格式）"""
        result = DocumentParseResult()
        result.error_message = "暂不支持DOC格式，请转换为DOCX格式"
        logger.warning(f"不支持的DOC格式: {file_path}")
        return result
    
    def _detect_language(self, content: str) -> str:
        """检测语言"""
        parser = TextDocumentParser()
        return parser._detect_language(content)
    
    def _extract_docx_metadata(self, doc) -> Dict:
        """提取DOCX元数据"""
        metadata = {}
        
        try:
            core_props = doc.core_properties
            
            if core_props.title:
                metadata['title'] = core_props.title
            if core_props.author:
                metadata['author'] = core_props.author
            if core_props.subject:
                metadata['subject'] = core_props.subject
            if core_props.created:
                metadata['created'] = str(core_props.created)
            if core_props.modified:
                metadata['modified'] = str(core_props.modified)
            if core_props.last_modified_by:
                metadata['last_modified_by'] = core_props.last_modified_by
        
        except Exception as e:
            logger.warning(f"提取DOCX元数据失败: {e}")
        
        return metadata


class DocumentParserManager:
    """文档解析器管理器"""
    
    def __init__(self):
        self.parsers: List[BaseDocumentParser] = [
            TextDocumentParser(),
            PDFDocumentParser(),
            WordDocumentParser(),
        ]
        logger.info(f"文档解析器管理器初始化完成，支持 {len(self.parsers)} 种解析器")
    
    def get_parser(self, file_path: Path) -> Optional[BaseDocumentParser]:
        """获取适合的解析器"""
        for parser in self.parsers:
            if parser.can_parse(file_path):
                return parser
        return None
    
    def parse_document(self, file_path: Path) -> DocumentParseResult:
        """解析文档"""
        parser = self.get_parser(file_path)
        
        if parser is None:
            result = DocumentParseResult()
            result.error_message = f"不支持的文档格式: {file_path.suffix}"
            logger.warning(result.error_message)
            return result
        
        logger.info(f"使用 {parser.__class__.__name__} 解析文档: {file_path}")
        return parser.parse(file_path)
    
    def get_supported_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        extensions = []
        for parser in self.parsers:
            if hasattr(parser, 'SUPPORTED_EXTENSIONS'):
                extensions.extend(parser.SUPPORTED_EXTENSIONS)
        
        # 添加其他已知支持的扩展名
        extensions.extend(['.pdf', '.docx', '.doc'])
        
        return list(set(extensions))


# 全局文档解析器管理器实例
document_parser = DocumentParserManager()
