"""
GUI测试脚本
GUI Test Script

测试前端界面是否能正常启动和显示
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import QApplication

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui():
    """测试GUI界面"""
    try:
        print("正在初始化应用程序...")
        app = QApplication(sys.argv)
        
        print("正在导入主窗口...")
        from frontend.ui.main_window import MainWindow
        
        print("正在创建主窗口...")
        window = MainWindow()
        
        print("正在显示主窗口...")
        window.show()
        
        print("GUI测试成功！窗口已显示。")
        print("按 Ctrl+C 退出程序")
        
        # 运行5秒后自动退出
        from PySide6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(5000)  # 5秒
        
        return app.exec()
        
    except Exception as e:
        print(f"GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_gui())
