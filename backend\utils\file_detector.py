"""
文件类型检测工具
File Type Detection Utilities

通过文件头标志、MIME类型等方式精准检测文档文件类型
"""

import os
import mimetypes
from pathlib import Path
from typing import Dict, Optional, Tuple, List
from loguru import logger

class FileTypeDetector:
    """文件类型检测器"""
    
    # 文件头标志字典 (文件类型: [可能的文件头标志])
    FILE_SIGNATURES = {
        'pdf': [b'%PDF'],
        'doc': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],  # Microsoft Office文档
        'docx': [b'PK\x03\x04'],  # ZIP格式（Office 2007+）
        'xls': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],
        'xlsx': [b'PK\x03\x04'],
        'ppt': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],
        'pptx': [b'PK\x03\x04'],
        'rtf': [b'{\\rtf'],
        'txt': [b'\xff\xfe', b'\xfe\xff', b'\xef\xbb\xbf'],  # UTF-16 LE, BE, UTF-8 BOM
        'epub': [b'PK\x03\x04'],
        'mobi': [b'BOOKMOBI'],
        'azw': [b'TPZ'],
        'azw3': [b'TPZ'],
        'fb2': [b'<?xml'],
        'html': [b'<!DOCTYPE', b'<html', b'<HTML'],
        'xml': [b'<?xml'],
        'md': [],  # Markdown通常是纯文本，没有特定文件头
        'odt': [b'PK\x03\x04'],  # OpenDocument格式
        'ods': [b'PK\x03\x04'],
        'odp': [b'PK\x03\x04'],
        'wps': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],  # WPS格式
        'ceb': [b'CEB'],  # 方正Apabi格式
    }
    
    # 支持的文档类型
    SUPPORTED_DOCUMENT_TYPES = {
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'rtf',
        'txt', 'epub', 'mobi', 'azw', 'azw3', 'fb2', 'html', 'xml',
        'md', 'odt', 'ods', 'odp', 'wps', 'ceb'
    }
    
    # MIME类型映射
    MIME_TYPE_MAPPING = {
        'application/pdf': 'pdf',
        'application/msword': 'doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
        'application/vnd.ms-excel': 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
        'application/vnd.ms-powerpoint': 'ppt',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
        'application/rtf': 'rtf',
        'text/plain': 'txt',
        'text/markdown': 'md',
        'text/html': 'html',
        'application/xml': 'xml',
        'text/xml': 'xml',
        'application/epub+zip': 'epub',
        'application/x-mobipocket-ebook': 'mobi',
        'application/vnd.oasis.opendocument.text': 'odt',
        'application/vnd.oasis.opendocument.spreadsheet': 'ods',
        'application/vnd.oasis.opendocument.presentation': 'odp',
    }
    
    def __init__(self):
        """初始化文件类型检测器"""
        # 初始化mimetypes
        mimetypes.init()
        logger.info("文件类型检测器初始化完成")
    
    def read_file_header(self, file_path: Path, max_bytes: int = 512) -> bytes:
        """
        读取文件头部字节
        
        Args:
            file_path: 文件路径
            max_bytes: 最大读取字节数
            
        Returns:
            bytes: 文件头部字节
        """
        try:
            with open(file_path, 'rb') as f:
                return f.read(max_bytes)
        except Exception as e:
            logger.warning(f"读取文件头失败 {file_path}: {e}")
            return b''
    
    def detect_by_signature(self, file_path: Path) -> Optional[str]:
        """
        通过文件头标志检测文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 检测到的文件类型，如果无法检测则返回None
        """
        header = self.read_file_header(file_path)
        if not header:
            return None
        
        for file_type, signatures in self.FILE_SIGNATURES.items():
            for signature in signatures:
                if header.startswith(signature):
                    return file_type
        
        # 特殊处理：检查ZIP格式的具体类型
        if header.startswith(b'PK\x03\x04'):
            return self._detect_zip_based_format(file_path)
        
        # 特殊处理：纯文本文件
        if self._is_text_file(header):
            extension = file_path.suffix.lower().lstrip('.')
            if extension in ['md', 'txt', 'html', 'xml', 'fb2']:
                return extension
            return 'txt'
        
        return None
    
    def _detect_zip_based_format(self, file_path: Path) -> str:
        """
        检测基于ZIP格式的文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 检测到的文件类型
        """
        extension = file_path.suffix.lower().lstrip('.')
        
        # 根据扩展名判断
        if extension in ['docx', 'xlsx', 'pptx', 'epub', 'odt', 'ods', 'odp']:
            return extension
        
        # 尝试读取ZIP内容来进一步判断
        try:
            import zipfile
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                file_list = zip_file.namelist()
                
                # Office 2007+ 格式检测
                if 'word/document.xml' in file_list:
                    return 'docx'
                elif 'xl/workbook.xml' in file_list:
                    return 'xlsx'
                elif 'ppt/presentation.xml' in file_list:
                    return 'pptx'
                elif 'META-INF/container.xml' in file_list:
                    return 'epub'
                elif 'meta.xml' in file_list and 'content.xml' in file_list:
                    # OpenDocument格式
                    if extension in ['odt', 'ods', 'odp']:
                        return extension
                    return 'odt'  # 默认为文本文档
                    
        except Exception as e:
            logger.debug(f"ZIP内容检测失败 {file_path}: {e}")
        
        return 'zip'  # 默认返回zip
    
    def _is_text_file(self, header: bytes) -> bool:
        """
        判断是否为文本文件
        
        Args:
            header: 文件头部字节
            
        Returns:
            bool: 是否为文本文件
        """
        # 检查是否包含过多的非文本字符
        try:
            # 尝试解码为UTF-8
            header.decode('utf-8')
            return True
        except UnicodeDecodeError:
            pass
        
        try:
            # 尝试解码为GBK
            header.decode('gbk')
            return True
        except UnicodeDecodeError:
            pass
        
        # 检查是否大部分字符都是可打印的ASCII字符
        printable_count = sum(1 for byte in header if 32 <= byte <= 126 or byte in [9, 10, 13])
        return printable_count / len(header) > 0.7 if header else False
    
    def detect_by_mime_type(self, file_path: Path) -> Optional[str]:
        """
        通过MIME类型检测文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 检测到的文件类型
        """
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if mime_type and mime_type in self.MIME_TYPE_MAPPING:
            return self.MIME_TYPE_MAPPING[mime_type]
        return None
    
    def detect_by_extension(self, file_path: Path) -> Optional[str]:
        """
        通过文件扩展名检测文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 检测到的文件类型
        """
        extension = file_path.suffix.lower().lstrip('.')
        if extension in self.SUPPORTED_DOCUMENT_TYPES:
            return extension
        return None
    
    def detect_file_type(self, file_path: Path) -> Tuple[str, str, float]:
        """
        综合检测文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[str, str, float]: (检测到的文件类型, 检测方法, 置信度)
        """
        if not file_path.exists():
            return 'unknown', 'file_not_found', 0.0
        
        # 1. 首先通过文件头标志检测（最可靠）
        detected_type = self.detect_by_signature(file_path)
        if detected_type:
            return detected_type, 'signature', 0.9
        
        # 2. 通过MIME类型检测
        detected_type = self.detect_by_mime_type(file_path)
        if detected_type:
            return detected_type, 'mime_type', 0.7
        
        # 3. 最后通过扩展名检测（最不可靠）
        detected_type = self.detect_by_extension(file_path)
        if detected_type:
            return detected_type, 'extension', 0.5
        
        return 'unknown', 'no_match', 0.0
    
    def is_document_file(self, file_path: Path) -> bool:
        """
        判断是否为支持的文档文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为支持的文档文件
        """
        file_type, _, confidence = self.detect_file_type(file_path)
        return file_type in self.SUPPORTED_DOCUMENT_TYPES and confidence > 0.0
    
    def get_file_info(self, file_path: Path) -> Dict:
        """
        获取文件的详细信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 文件信息字典
        """
        file_type, detection_method, confidence = self.detect_file_type(file_path)
        
        info = {
            'file_path': str(file_path),
            'file_name': file_path.name,
            'file_extension': file_path.suffix.lower().lstrip('.'),
            'detected_type': file_type,
            'detection_method': detection_method,
            'confidence': confidence,
            'is_document': file_type in self.SUPPORTED_DOCUMENT_TYPES,
            'file_size': 0,
            'mime_type': None
        }
        
        try:
            stat = file_path.stat()
            info['file_size'] = stat.st_size
            info['modified_time'] = stat.st_mtime
        except Exception as e:
            logger.warning(f"获取文件状态失败 {file_path}: {e}")
        
        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(str(file_path))
        info['mime_type'] = mime_type
        
        return info


# 全局文件类型检测器实例
file_detector = FileTypeDetector()
