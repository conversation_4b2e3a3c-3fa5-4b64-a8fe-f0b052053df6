"""
文档处理服务
Document Processing Service

整合文档解析、分析、存储等功能的高级服务
"""

import hashlib
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

from backend.core.document_parser import document_parser, DocumentParseResult
from backend.core.document_analyzer import document_analyzer, DocumentInfo
from backend.utils.file_detector import file_detector
from backend.models.document import Document
from backend.core.database import db_manager


class DocumentProcessingResult:
    """文档处理结果类"""
    
    def __init__(self):
        self.success: bool = False
        self.document_id: Optional[int] = None
        self.error_message: str = ""
        self.processing_time: float = 0.0
        self.document_info: Optional[DocumentInfo] = None
        self.parse_result: Optional[DocumentParseResult] = None


class DocumentService:
    """文档处理服务类"""
    
    def __init__(self):
        logger.info("文档处理服务初始化完成")
    
    def process_document(self, file_path: Path) -> DocumentProcessingResult:
        """
        处理单个文档
        
        Args:
            file_path: 文件路径
            
        Returns:
            DocumentProcessingResult: 处理结果
        """
        result = DocumentProcessingResult()
        start_time = datetime.now()
        
        try:
            logger.info(f"开始处理文档: {file_path}")
            
            # 1. 检查文件是否存在
            if not file_path.exists():
                result.error_message = f"文件不存在: {file_path}"
                return result
            
            # 2. 检测文件类型
            if not file_detector.is_document_file(file_path):
                result.error_message = f"不支持的文档类型: {file_path}"
                return result
            
            # 3. 计算文件哈希
            file_hash = self._calculate_file_hash(file_path)
            
            # 4. 检查是否已经处理过
            existing_doc = self._find_existing_document(file_path, file_hash)
            if existing_doc:
                logger.info(f"文档已存在，跳过处理: {file_path}")
                result.success = True
                result.document_id = existing_doc.id
                return result
            
            # 5. 解析文档内容
            parse_result = document_parser.parse_document(file_path)
            result.parse_result = parse_result
            
            if not parse_result.success:
                result.error_message = f"文档解析失败: {parse_result.error_message}"
                return result
            
            # 6. 分析文档信息
            document_info = document_analyzer.analyze_document(parse_result, file_path)
            result.document_info = document_info
            
            # 7. 保存到数据库
            document_id = self._save_document_to_database(
                file_path, file_hash, parse_result, document_info
            )
            
            if document_id:
                result.success = True
                result.document_id = document_id
                logger.info(f"文档处理成功: {file_path} -> ID: {document_id}")
            else:
                result.error_message = "保存到数据库失败"
            
        except Exception as e:
            result.error_message = f"处理文档时出错: {str(e)}"
            logger.error(result.error_message)
        
        finally:
            end_time = datetime.now()
            result.processing_time = (end_time - start_time).total_seconds()
            logger.info(f"文档处理耗时: {result.processing_time:.2f}秒")
        
        return result
    
    def process_documents_batch(self, file_paths: List[Path]) -> List[DocumentProcessingResult]:
        """
        批量处理文档
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            List[DocumentProcessingResult]: 处理结果列表
        """
        results = []
        
        logger.info(f"开始批量处理 {len(file_paths)} 个文档")
        
        for i, file_path in enumerate(file_paths, 1):
            logger.info(f"处理进度: {i}/{len(file_paths)} - {file_path.name}")
            result = self.process_document(file_path)
            results.append(result)
        
        # 统计结果
        success_count = sum(1 for r in results if r.success)
        logger.info(f"批量处理完成: 成功 {success_count}/{len(file_paths)}")
        
        return results
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.warning(f"计算文件哈希失败 {file_path}: {e}")
            return ""
    
    def _find_existing_document(self, file_path: Path, file_hash: str) -> Optional[Document]:
        """查找已存在的文档"""
        try:
            session = db_manager.get_session()
            
            # 首先按路径查找
            doc = session.query(Document).filter(
                Document.file_path == str(file_path)
            ).first()
            
            if doc:
                return doc
            
            # 按哈希值查找（可能是重复文件）
            if file_hash:
                doc = session.query(Document).filter(
                    Document.file_hash == file_hash
                ).first()
                
                if doc:
                    return doc
            
        except Exception as e:
            logger.warning(f"查找已存在文档失败: {e}")
        finally:
            session.close()
        
        return None
    
    def _save_document_to_database(
        self, 
        file_path: Path, 
        file_hash: str, 
        parse_result: DocumentParseResult, 
        document_info: DocumentInfo
    ) -> Optional[int]:
        """保存文档信息到数据库"""
        try:
            session = db_manager.get_session()
            
            # 获取文件信息
            file_info = file_detector.get_file_info(file_path)
            
            # 创建文档记录
            document = Document(
                file_path=str(file_path),
                file_name=file_path.name,
                file_extension=file_path.suffix.lower().lstrip('.'),
                file_size=file_info.get('file_size', 0),
                file_hash=file_hash,
                detected_type=file_info.get('detected_type', 'unknown'),
                mime_type=file_info.get('mime_type'),
                
                # 文档信息
                document_type=document_info.document_type,
                title=document_info.title,
                author=document_info.author,
                publisher=document_info.publisher,
                publish_date=document_info.publish_date,
                language=document_info.language,
                page_count=parse_result.page_count,
                word_count=parse_result.word_count,
                
                # 内容信息
                summary="",  # 暂时为空，后续添加摘要生成功能
                summary_chinese="",
                content_preview=parse_result.content[:1000] if parse_result.content else "",
                
                # 状态信息
                is_processed=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            session.add(document)
            session.commit()
            
            document_id = document.id
            logger.info(f"文档保存成功: ID={document_id}")
            
            return document_id
            
        except Exception as e:
            logger.error(f"保存文档到数据库失败: {e}")
            session.rollback()
            return None
        finally:
            session.close()
    
    def get_document_by_id(self, document_id: int) -> Optional[Document]:
        """根据ID获取文档"""
        try:
            session = db_manager.get_session()
            document = session.query(Document).filter(Document.id == document_id).first()
            return document
        except Exception as e:
            logger.error(f"获取文档失败: {e}")
            return None
        finally:
            session.close()
    
    def get_documents_by_type(self, document_type: str) -> List[Document]:
        """根据类型获取文档列表"""
        try:
            session = db_manager.get_session()
            documents = session.query(Document).filter(
                Document.document_type == document_type
            ).all()
            return documents
        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            return []
        finally:
            session.close()
    
    def search_documents(self, keyword: str) -> List[Document]:
        """搜索文档"""
        try:
            session = db_manager.get_session()
            documents = session.query(Document).filter(
                Document.title.contains(keyword) |
                Document.author.contains(keyword) |
                Document.content_preview.contains(keyword)
            ).all()
            return documents
        except Exception as e:
            logger.error(f"搜索文档失败: {e}")
            return []
        finally:
            session.close()
    
    def get_processing_statistics(self) -> Dict:
        """获取处理统计信息"""
        try:
            session = db_manager.get_session()
            
            total_docs = session.query(Document).count()
            processed_docs = session.query(Document).filter(Document.is_processed == True).count()
            
            # 按类型统计
            type_stats = {}
            for doc_type in ['书籍', '论文', '报告', '总结', '手册']:
                count = session.query(Document).filter(Document.document_type == doc_type).count()
                if count > 0:
                    type_stats[doc_type] = count
            
            # 按语言统计
            language_stats = {}
            for lang in ['zh', 'en', 'mixed']:
                count = session.query(Document).filter(Document.language == lang).count()
                if count > 0:
                    language_stats[lang] = count
            
            return {
                'total_documents': total_docs,
                'processed_documents': processed_docs,
                'document_types': type_stats,
                'languages': language_stats
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
        finally:
            session.close()


# 全局文档服务实例
document_service = DocumentService()
