"""
个人文档管理系统主程序
Personal Document Management System Main Application

演示当前已实现的功能
"""

import sys
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from backend.core.database import init_database, db_manager
from backend.core.file_scanner import file_scanner
from backend.utils.file_detector import file_detector


def setup_logging():
    """设置日志"""
    logger.remove()  # 移除默认处理器
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    logger.add(
        "logs/app.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB",
        retention="7 days"
    )


def demo_file_detection():
    """演示文件类型检测功能"""
    logger.info("=== 文件类型检测演示 ===")
    
    # 获取当前目录下的一些文件进行测试
    current_dir = Path(".")
    test_files = [
        current_dir / "README.md",
        current_dir / "pyproject.toml",
        current_dir / "LICENSE"
    ]
    
    for file_path in test_files:
        if file_path.exists():
            logger.info(f"检测文件: {file_path}")
            file_type, method, confidence = file_detector.detect_file_type(file_path)
            is_doc = file_detector.is_document_file(file_path)
            
            logger.info(f"  文件类型: {file_type}")
            logger.info(f"  检测方法: {method}")
            logger.info(f"  置信度: {confidence}")
            logger.info(f"  是否为文档: {is_doc}")
            logger.info("")


def demo_file_scanning():
    """演示文件扫描功能"""
    logger.info("=== 文件扫描演示 ===")
    
    # 扫描当前目录
    current_dir = Path(".")
    logger.info(f"扫描目录: {current_dir.absolute()}")
    
    # 获取目录统计信息
    stats = file_scanner.get_directory_stats(current_dir)
    logger.info(f"目录统计信息:")
    logger.info(f"  总文件数: {stats['total_files']}")
    logger.info(f"  总大小: {stats['total_size']} 字节")
    logger.info(f"  文档文件数: {stats['document_files']}")
    logger.info(f"  文档总大小: {stats['document_size']} 字节")
    logger.info(f"  文件类型分布: {stats['file_types']}")
    
    if stats['largest_file']:
        logger.info(f"  最大文件: {stats['largest_file']} ({stats['largest_file_size']} 字节)")
    
    logger.info("")
    
    # 扫描文档文件
    logger.info("扫描文档文件...")
    documents = file_scanner.scan_for_documents([current_dir], recursive=False)
    
    logger.info(f"找到 {len(documents)} 个文档文件:")
    for doc in documents[:5]:  # 只显示前5个
        logger.info(f"  {doc['file_name']} ({doc['detected_type']}) - {doc['file_size']} 字节")


def demo_database():
    """演示数据库功能"""
    logger.info("=== 数据库功能演示 ===")
    
    # 初始化数据库
    if init_database():
        logger.info("数据库初始化成功")
        
        # 获取数据库信息
        db_info = db_manager.get_database_info()
        logger.info(f"数据库信息:")
        logger.info(f"  路径: {db_info['database_path']}")
        logger.info(f"  大小: {db_info['database_size']} 字节")
        logger.info(f"  表数量: {db_info['table_count']}")
        logger.info(f"  存在: {db_info['exists']}")
    else:
        logger.error("数据库初始化失败")


def main():
    """主函数"""
    logger.info("个人文档管理系统启动")
    logger.info("Personal Document Management System Starting")
    
    # 创建日志目录
    Path("logs").mkdir(exist_ok=True)
    
    try:
        # 演示各个功能模块
        demo_file_detection()
        demo_file_scanning()
        demo_database()
        
        logger.info("演示完成！")
        logger.info("Demo completed!")
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    setup_logging()
    sys.exit(main())
