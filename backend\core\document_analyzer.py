"""
文档信息分析器
Document Information Analyzer

分析文档内容，提取标题、作者、出版信息、文档类型等高级信息
"""

import re
from pathlib import Path
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass

try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

from backend.core.document_parser import DocumentParseResult


@dataclass
class DocumentInfo:
    """文档信息类"""
    title: str = ""
    author: str = ""
    publisher: str = ""
    publish_date: str = ""
    document_type: str = ""  # 书籍、报告、论文、总结等
    language: str = ""
    subject: str = ""
    keywords: List[str] = None
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []


class DocumentAnalyzer:
    """文档信息分析器"""
    
    def __init__(self):
        # 文档类型关键词模式
        self.document_type_patterns = {
            '论文': [
                r'摘\s*要', r'abstract', r'关键词', r'keywords', r'引言', r'introduction',
                r'参考文献', r'references', r'结论', r'conclusion', r'致谢', r'acknowledgment'
            ],
            '书籍': [
                r'目\s*录', r'contents', r'前\s*言', r'preface', r'序', r'chapter',
                r'第.*章', r'第.*节', r'附录', r'appendix', r'索引', r'index'
            ],
            '报告': [
                r'执行摘要', r'executive summary', r'背景', r'background', r'建议', r'recommendation',
                r'分析', r'analysis', r'总结', r'summary', r'结果', r'result'
            ],
            '总结': [
                r'总结', r'summary', r'概述', r'overview', r'要点', r'key points',
                r'心得', r'体会', r'感想', r'reflection'
            ],
            '手册': [
                r'使用说明', r'manual', r'指南', r'guide', r'操作', r'operation',
                r'步骤', r'step', r'流程', r'process', r'方法', r'method'
            ]
        }
        
        # 作者信息模式
        self.author_patterns = [
            r'作\s*者[：:]\s*([^\n\r]+)',
            r'著\s*者[：:]\s*([^\n\r]+)',
            r'编\s*者[：:]\s*([^\n\r]+)',
            r'主\s*编[：:]\s*([^\n\r]+)',
            r'author[：:]\s*([^\n\r]+)',
            r'by\s+([A-Za-z\s\.]+)',
        ]
        
        # 出版信息模式
        self.publisher_patterns = [
            r'出版社[：:]\s*([^\n\r]+)',
            r'出版者[：:]\s*([^\n\r]+)',
            r'publisher[：:]\s*([^\n\r]+)',
            r'([^\n\r]*出版社)',
            r'([^\n\r]*出版公司)',
            r'([^\n\r]*Press)',
            r'([^\n\r]*Publishing)',
        ]
        
        # 出版日期模式
        self.date_patterns = [
            r'出版日期[：:]\s*([^\n\r]+)',
            r'出版时间[：:]\s*([^\n\r]+)',
            r'发表时间[：:]\s*([^\n\r]+)',
            r'(\d{4}年\d{1,2}月)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}\.\d{1,2}\.\d{1,2})',
            r'(20\d{2})',  # 2000-2099年
        ]
        
        logger.info("文档信息分析器初始化完成")
    
    def analyze_document(self, parse_result: DocumentParseResult, file_path: Path) -> DocumentInfo:
        """
        分析文档信息
        
        Args:
            parse_result: 文档解析结果
            file_path: 文件路径
            
        Returns:
            DocumentInfo: 文档信息
        """
        info = DocumentInfo()
        
        if not parse_result.success or not parse_result.content:
            logger.warning(f"文档解析失败或内容为空: {file_path}")
            return info
        
        content = parse_result.content
        
        # 分析文档类型
        info.document_type = self._analyze_document_type(content)
        
        # 提取标题
        info.title = self._extract_title(content, parse_result.metadata, file_path)
        
        # 提取作者
        info.author = self._extract_author(content, parse_result.metadata)
        
        # 提取出版信息
        info.publisher = self._extract_publisher(content, parse_result.metadata)
        
        # 提取出版日期
        info.publish_date = self._extract_publish_date(content, parse_result.metadata)
        
        # 设置语言
        info.language = parse_result.language
        
        # 提取主题
        info.subject = self._extract_subject(content, parse_result.metadata)
        
        logger.info(f"文档分析完成: {file_path.name} - 类型: {info.document_type}, 标题: {info.title[:50]}...")
        
        return info
    
    def _analyze_document_type(self, content: str) -> str:
        """分析文档类型"""
        content_lower = content.lower()
        type_scores = {}
        
        for doc_type, patterns in self.document_type_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, content_lower, re.IGNORECASE))
                score += matches
            type_scores[doc_type] = score
        
        # 找到得分最高的文档类型
        if type_scores:
            best_type = max(type_scores, key=type_scores.get)
            if type_scores[best_type] > 0:
                return best_type
        
        # 根据内容长度和结构进一步判断
        if len(content) > 50000:  # 长文档可能是书籍
            return '书籍'
        elif len(content) > 10000:  # 中等长度可能是报告或论文
            if '参考文献' in content or 'references' in content_lower:
                return '论文'
            else:
                return '报告'
        else:  # 短文档可能是总结
            return '总结'
    
    def _extract_title(self, content: str, metadata: Dict, file_path: Path) -> str:
        """提取文档标题"""
        # 1. 首先检查元数据
        if metadata.get('title'):
            title = str(metadata['title']).strip()
            if title and title != file_path.stem:
                return title
        
        # 2. 从内容开头提取标题
        lines = content.split('\n')
        for i, line in enumerate(lines[:10]):  # 检查前10行
            line = line.strip()
            if not line:
                continue
            
            # Markdown标题
            if line.startswith('#'):
                return line.lstrip('#').strip()
            
            # 第一个非空行，且长度适中
            if 5 <= len(line) <= 100 and not line.startswith(('作者', '时间', '日期')):
                # 检查是否像标题（没有句号结尾，不全是数字等）
                if not line.endswith(('。', '.', '！', '!', '？', '?')) and not line.isdigit():
                    return line
        
        # 3. 使用文件名作为标题
        return file_path.stem
    
    def _extract_author(self, content: str, metadata: Dict) -> str:
        """提取作者信息"""
        # 1. 检查元数据
        if metadata.get('author'):
            return str(metadata['author']).strip()
        
        # 2. 使用正则表达式从内容中提取
        for pattern in self.author_patterns:
            matches = re.search(pattern, content, re.IGNORECASE)
            if matches:
                author = matches.group(1).strip()
                # 清理作者信息
                author = re.sub(r'[，,；;]\s*$', '', author)  # 移除末尾的标点
                if author and len(author) <= 50:  # 合理的作者名长度
                    return author
        
        return ""
    
    def _extract_publisher(self, content: str, metadata: Dict) -> str:
        """提取出版社信息"""
        # 1. 检查元数据
        if metadata.get('publisher'):
            return str(metadata['publisher']).strip()
        
        # 2. 使用正则表达式从内容中提取
        for pattern in self.publisher_patterns:
            matches = re.search(pattern, content, re.IGNORECASE)
            if matches:
                publisher = matches.group(1).strip()
                # 清理出版社信息
                publisher = re.sub(r'[，,；;]\s*$', '', publisher)
                if publisher and len(publisher) <= 100:
                    return publisher
        
        return ""
    
    def _extract_publish_date(self, content: str, metadata: Dict) -> str:
        """提取出版日期"""
        # 1. 检查元数据
        for key in ['creation_date', 'created', 'publish_date']:
            if metadata.get(key):
                date_str = str(metadata[key]).strip()
                # 简单的日期格式化
                if date_str:
                    return self._format_date(date_str)
        
        # 2. 使用正则表达式从内容中提取
        for pattern in self.date_patterns:
            matches = re.search(pattern, content)
            if matches:
                date_str = matches.group(1).strip()
                if date_str:
                    return self._format_date(date_str)
        
        return ""
    
    def _format_date(self, date_str: str) -> str:
        """格式化日期字符串"""
        # 移除常见的前缀
        date_str = re.sub(r'^D:', '', date_str)  # PDF日期前缀
        
        # 提取年份
        year_match = re.search(r'(20\d{2})', date_str)
        if year_match:
            return year_match.group(1)
        
        return date_str[:20]  # 限制长度
    
    def _extract_subject(self, content: str, metadata: Dict) -> str:
        """提取主题/学科信息"""
        # 1. 检查元数据
        if metadata.get('subject'):
            return str(metadata['subject']).strip()
        
        # 2. 简单的主题推断（基于关键词）
        subject_keywords = {
            '计算机': ['计算机', '软件', '编程', 'computer', 'software', 'programming'],
            '数学': ['数学', '算法', '统计', 'mathematics', 'algorithm', 'statistics'],
            '物理': ['物理', '力学', '光学', 'physics', 'mechanics', 'optics'],
            '化学': ['化学', '分子', '反应', 'chemistry', 'molecule', 'reaction'],
            '生物': ['生物', '细胞', '基因', 'biology', 'cell', 'gene'],
            '经济': ['经济', '金融', '市场', 'economics', 'finance', 'market'],
            '管理': ['管理', '企业', '组织', 'management', 'business', 'organization'],
            '文学': ['文学', '小说', '诗歌', 'literature', 'novel', 'poetry'],
            '历史': ['历史', '古代', '近代', 'history', 'ancient', 'modern'],
        }
        
        content_lower = content.lower()
        subject_scores = {}
        
        for subject, keywords in subject_keywords.items():
            score = 0
            for keyword in keywords:
                score += content_lower.count(keyword.lower())
            subject_scores[subject] = score
        
        if subject_scores:
            best_subject = max(subject_scores, key=subject_scores.get)
            if subject_scores[best_subject] > 2:  # 至少出现3次
                return best_subject
        
        return ""
    
    def find_table_of_contents(self, content: str) -> Optional[int]:
        """
        查找目录位置
        
        Args:
            content: 文档内容
            
        Returns:
            Optional[int]: 目录开始位置，如果没找到返回None
        """
        toc_patterns = [
            r'目\s*录',
            r'contents',
            r'table\s+of\s+contents',
            r'章节目录',
            r'内容目录'
        ]
        
        for pattern in toc_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.start()
        
        return None
    
    def extract_content_before_toc(self, content: str) -> str:
        """
        提取目录之前的内容
        
        Args:
            content: 文档内容
            
        Returns:
            str: 目录之前的内容
        """
        toc_position = self.find_table_of_contents(content)
        
        if toc_position is not None:
            return content[:toc_position]
        
        # 如果没有找到目录，返回前面的一部分内容
        return content[:2000]  # 前2000字符


# 全局文档分析器实例
document_analyzer = DocumentAnalyzer()
