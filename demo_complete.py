"""
完整功能演示
Complete Features Demo

演示文档管理系统的所有核心功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from backend.core.document_parser import document_parser
    from backend.core.document_analyzer import document_analyzer
    from backend.core.nlp_processor import nlp_processor
    from backend.core.duplicate_detector import duplicate_detector
    from backend.core.file_scanner import file_scanner
    from backend.utils.file_detector import file_detector
    FULL_FEATURES = True
except ImportError as e:
    print(f"导入模块失败: {e}")
    FULL_FEATURES = False


class CompleteDemo:
    """完整功能演示类"""
    
    def __init__(self):
        self.sample_docs = []
        
    def create_comprehensive_samples(self):
        """创建综合示例文档"""
        samples_dir = Path("samples")
        samples_dir.mkdir(exist_ok=True)
        
        # 示例1：技术论文
        paper_content = """# 基于深度学习的自然语言处理技术研究

作者：王小明, 李小红
单位：清华大学计算机科学与技术系
出版社：计算机学报
发表时间：2024年3月

## 摘要

本文研究了基于深度学习的自然语言处理技术，包括词向量表示、循环神经网络、注意力机制等关键技术。通过实验验证了所提出方法的有效性。

关键词：深度学习, 自然语言处理, 神经网络, 注意力机制

## 1. 引言

自然语言处理（Natural Language Processing, NLP）是人工智能领域的重要分支。近年来，深度学习技术在NLP领域取得了显著进展。

### 1.1 研究背景

传统的NLP方法主要基于规则和统计方法，存在特征工程复杂、泛化能力有限等问题。

### 1.2 研究意义

深度学习方法能够自动学习文本的语义表示，提高了NLP任务的性能。

## 2. 相关工作

### 2.1 词向量表示

Word2Vec、GloVe等方法为词汇提供了密集的向量表示。

### 2.2 循环神经网络

LSTM、GRU等循环神经网络能够处理序列数据。

### 2.3 注意力机制

注意力机制允许模型关注输入序列的重要部分。

## 3. 方法

本文提出了一种基于Transformer的文本分类方法。

### 3.1 模型架构

模型包含编码器和分类器两个部分。

### 3.2 训练策略

采用预训练+微调的策略进行模型训练。

## 4. 实验

### 4.1 数据集

使用了多个公开数据集进行实验验证。

### 4.2 实验结果

实验结果表明，所提出的方法在多个任务上都取得了最佳性能。

## 5. 结论

本文提出的基于深度学习的NLP方法具有良好的性能和泛化能力。

## 参考文献

[1] Vaswani, A., et al. Attention is all you need. NIPS 2017.
[2] Devlin, J., et al. BERT: Pre-training of Deep Bidirectional Transformers. NAACL 2019.
"""
        
        # 示例2：技术手册
        manual_content = """Python编程入门指南

作者：编程小组
出版社：技术出版社
版本：v2.0
更新时间：2024年6月

目录

第1章 Python基础
第2章 数据类型
第3章 控制结构
第4章 函数
第5章 面向对象编程
第6章 文件操作
第7章 异常处理
第8章 模块和包

第1章 Python基础

1.1 什么是Python

Python是一种高级编程语言，具有简洁、易读的语法特点。

1.2 安装Python

可以从官网 https://python.org 下载Python安装包。

1.3 第一个程序

```python
print("Hello, World!")
```

第2章 数据类型

2.1 数字类型

Python支持整数、浮点数、复数等数字类型。

2.2 字符串

字符串是字符的序列，用引号包围。

2.3 列表

列表是有序的可变序列。

2.4 字典

字典是键值对的集合。

第3章 控制结构

3.1 条件语句

使用if、elif、else进行条件判断。

3.2 循环语句

使用for、while进行循环操作。

第4章 函数

4.1 定义函数

使用def关键字定义函数。

4.2 参数传递

函数可以接收位置参数和关键字参数。

第5章 面向对象编程

5.1 类和对象

类是对象的模板，对象是类的实例。

5.2 继承

子类可以继承父类的属性和方法。

总结

Python是一门优秀的编程语言，适合初学者学习。通过本指南的学习，读者可以掌握Python的基本语法和编程技巧。
"""
        
        # 示例3：重复文档（用于测试重复检测）
        duplicate_content = """# 基于深度学习的自然语言处理技术研究

作者：王小明, 李小红  
单位：清华大学计算机科学与技术系
出版社：计算机学报
发表时间：2024年3月

## 摘要

本文研究了基于深度学习的自然语言处理技术，包括词向量表示、循环神经网络、注意力机制等关键技术。通过实验验证了所提出方法的有效性。

关键词：深度学习, 自然语言处理, 神经网络, 注意力机制
"""
        
        # 保存示例文档
        files = [
            ("nlp_paper.md", paper_content),
            ("python_manual.txt", manual_content),
            ("nlp_paper_copy.md", duplicate_content)
        ]
        
        created_files = []
        for filename, content in files:
            file_path = samples_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            created_files.append(file_path)
        
        print(f"创建了 {len(created_files)} 个示例文档")
        return created_files
    
    def demo_complete_workflow(self, file_paths):
        """演示完整的文档处理工作流"""
        print("\n" + "="*60)
        print("完整文档处理工作流演示")
        print("="*60)
        
        if not FULL_FEATURES:
            print("需要安装完整依赖包才能运行完整演示")
            return
        
        processed_docs = []
        
        for file_path in file_paths:
            print(f"\n处理文档: {file_path.name}")
            print("-" * 40)
            
            # 1. 文件类型检测
            file_info = file_detector.get_file_info(file_path)
            print(f"文件类型: {file_info['detected_type']}")
            print(f"文件大小: {file_info['file_size']} 字节")
            
            # 2. 文档解析
            parse_result = document_parser.parse_document(file_path)
            if not parse_result.success:
                print(f"解析失败: {parse_result.error_message}")
                continue
            
            print(f"解析成功 - 内容长度: {len(parse_result.content)} 字符")
            
            # 3. 文档分析
            doc_info = document_analyzer.analyze_document(parse_result, file_path)
            print(f"标题: {doc_info.title}")
            print(f"作者: {doc_info.author}")
            print(f"类型: {doc_info.document_type}")
            print(f"语言: {doc_info.language}")
            
            # 4. 关键词提取
            keywords = nlp_processor.extract_keywords(parse_result.content, max_keywords=10)
            print(f"关键词: {', '.join([f'{word}({freq})' for word, freq in keywords[:5]])}")
            
            # 5. 摘要生成
            summary = nlp_processor.generate_summary(parse_result.content, max_sentences=2)
            print(f"摘要: {summary[:100]}...")
            
            # 保存处理结果
            doc_data = {
                'file_path': str(file_path),
                'file_hash': self._calculate_hash(file_path),
                'title': doc_info.title,
                'author': doc_info.author,
                'content_preview': parse_result.content[:1000],
                'file_size': file_info['file_size'],
                'keywords': keywords
            }
            processed_docs.append(doc_data)
        
        # 6. 重复文档检测
        print(f"\n重复文档检测")
        print("-" * 40)
        duplicates = duplicate_detector.detect_duplicates(processed_docs)
        
        if duplicates:
            print(f"发现 {len(duplicates)} 对重复文档:")
            for dup in duplicates:
                print(f"  {Path(dup.document1_path).name} <-> {Path(dup.document2_path).name}")
                print(f"    相似度: {dup.similarity_score:.2f} (类型: {dup.similarity_type})")
        else:
            print("未发现重复文档")
        
        return processed_docs
    
    def demo_nlp_features(self, text_sample):
        """演示NLP功能"""
        print(f"\n" + "="*60)
        print("NLP功能演示")
        print("="*60)
        
        if not FULL_FEATURES:
            print("需要安装完整依赖包才能运行NLP演示")
            return
        
        print(f"文本样本: {text_sample[:200]}...")
        
        # 关键词提取
        keywords = nlp_processor.extract_keywords(text_sample, max_keywords=15)
        print(f"\n关键词提取 (前10个):")
        for word, freq in keywords[:10]:
            print(f"  {word}: {freq}")
        
        # 摘要生成
        summary = nlp_processor.generate_summary(text_sample, max_sentences=3)
        print(f"\n自动摘要:")
        print(f"  {summary}")
        
        # 语言检测
        lang_details = nlp_processor.detect_language_detailed(text_sample)
        print(f"\n语言分析:")
        for lang, ratio in lang_details.items():
            if ratio > 0.01:  # 只显示比例大于1%的
                print(f"  {lang}: {ratio:.2%}")
        
        # 命名实体识别
        entities = nlp_processor.extract_named_entities(text_sample)
        print(f"\n命名实体识别:")
        for entity_type, entity_list in entities.items():
            if entity_list:
                print(f"  {entity_type}: {', '.join(entity_list[:5])}")
    
    def _calculate_hash(self, file_path):
        """计算文件哈希"""
        import hashlib
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def demo_performance_analysis(self, file_paths):
        """性能分析演示"""
        print(f"\n" + "="*60)
        print("性能分析")
        print("="*60)
        
        total_start = time.time()
        
        for file_path in file_paths:
            start_time = time.time()
            
            if FULL_FEATURES:
                # 完整处理流程
                parse_result = document_parser.parse_document(file_path)
                if parse_result.success:
                    doc_info = document_analyzer.analyze_document(parse_result, file_path)
                    keywords = nlp_processor.extract_keywords(parse_result.content, max_keywords=10)
                    summary = nlp_processor.generate_summary(parse_result.content, max_sentences=2)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"{file_path.name}: {processing_time:.3f}秒")
        
        total_time = time.time() - total_start
        print(f"\n总处理时间: {total_time:.3f}秒")
        print(f"平均处理时间: {total_time/len(file_paths):.3f}秒")


def main():
    """主函数"""
    print("个人文档管理系统 - 完整功能演示")
    print("Personal Document Management System - Complete Demo")
    
    demo = CompleteDemo()
    
    try:
        # 创建示例文档
        sample_files = demo.create_comprehensive_samples()
        
        # 完整工作流演示
        processed_docs = demo.demo_complete_workflow(sample_files)
        
        # NLP功能演示
        if sample_files:
            with open(sample_files[0], 'r', encoding='utf-8') as f:
                sample_text = f.read()
            demo.demo_nlp_features(sample_text)
        
        # 性能分析
        demo.demo_performance_analysis(sample_files)
        
        print(f"\n" + "="*60)
        print("演示完成！")
        print("Demo completed!")
        
        if not FULL_FEATURES:
            print("\n要体验完整功能，请安装依赖:")
            print("pip install -r requirements.txt")
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
