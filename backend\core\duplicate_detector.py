"""
重复文档检测器
Duplicate Document Detector

检测重复或相似的文档
"""

import hashlib
import re
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from dataclasses import dataclass

try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


@dataclass
class DuplicateMatch:
    """重复匹配结果"""
    document1_path: str
    document2_path: str
    similarity_score: float
    similarity_type: str  # 'hash', 'content', 'title', 'fuzzy'
    details: Dict = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}


class DuplicateDetector:
    """重复文档检测器"""
    
    def __init__(self):
        self.similarity_threshold = 0.8  # 相似度阈值
        logger.info("重复文档检测器初始化完成")
    
    def detect_duplicates(self, documents: List[Dict]) -> List[DuplicateMatch]:
        """
        检测重复文档
        
        Args:
            documents: 文档信息列表，每个文档包含路径、内容、哈希等信息
            
        Returns:
            List[DuplicateMatch]: 重复匹配结果列表
        """
        duplicates = []
        
        logger.info(f"开始检测 {len(documents)} 个文档的重复情况")
        
        # 1. 哈希值检测（完全相同的文件）
        hash_duplicates = self._detect_hash_duplicates(documents)
        duplicates.extend(hash_duplicates)
        
        # 2. 标题相似度检测
        title_duplicates = self._detect_title_duplicates(documents)
        duplicates.extend(title_duplicates)
        
        # 3. 内容相似度检测
        content_duplicates = self._detect_content_duplicates(documents)
        duplicates.extend(content_duplicates)
        
        # 去重（避免同一对文档被多次检测到）
        unique_duplicates = self._remove_duplicate_matches(duplicates)
        
        logger.info(f"检测完成，发现 {len(unique_duplicates)} 对重复文档")
        
        return unique_duplicates
    
    def _detect_hash_duplicates(self, documents: List[Dict]) -> List[DuplicateMatch]:
        """基于文件哈希检测完全相同的文档"""
        duplicates = []
        hash_groups = {}
        
        # 按哈希值分组
        for doc in documents:
            file_hash = doc.get('file_hash', '')
            if file_hash and len(file_hash) > 0:
                if file_hash not in hash_groups:
                    hash_groups[file_hash] = []
                hash_groups[file_hash].append(doc)
        
        # 找出有多个文档的哈希组
        for file_hash, docs in hash_groups.items():
            if len(docs) > 1:
                # 生成所有配对
                for i in range(len(docs)):
                    for j in range(i + 1, len(docs)):
                        match = DuplicateMatch(
                            document1_path=docs[i]['file_path'],
                            document2_path=docs[j]['file_path'],
                            similarity_score=1.0,
                            similarity_type='hash',
                            details={'hash': file_hash}
                        )
                        duplicates.append(match)
        
        logger.info(f"哈希检测发现 {len(duplicates)} 对完全相同的文档")
        return duplicates
    
    def _detect_title_duplicates(self, documents: List[Dict]) -> List[DuplicateMatch]:
        """基于标题相似度检测重复文档"""
        duplicates = []
        
        for i in range(len(documents)):
            for j in range(i + 1, len(documents)):
                doc1 = documents[i]
                doc2 = documents[j]
                
                title1 = doc1.get('title', '').strip()
                title2 = doc2.get('title', '').strip()
                
                if not title1 or not title2:
                    continue
                
                # 计算标题相似度
                similarity = self._calculate_text_similarity(title1, title2)
                
                if similarity >= self.similarity_threshold:
                    match = DuplicateMatch(
                        document1_path=doc1['file_path'],
                        document2_path=doc2['file_path'],
                        similarity_score=similarity,
                        similarity_type='title',
                        details={
                            'title1': title1,
                            'title2': title2
                        }
                    )
                    duplicates.append(match)
        
        logger.info(f"标题检测发现 {len(duplicates)} 对相似文档")
        return duplicates
    
    def _detect_content_duplicates(self, documents: List[Dict]) -> List[DuplicateMatch]:
        """基于内容相似度检测重复文档"""
        duplicates = []
        
        # 为了提高效率，只对较小的文档进行内容比较
        small_docs = [doc for doc in documents if doc.get('file_size', 0) < 1024 * 1024]  # 小于1MB
        
        for i in range(len(small_docs)):
            for j in range(i + 1, len(small_docs)):
                doc1 = small_docs[i]
                doc2 = small_docs[j]
                
                # 跳过已经通过哈希检测到的重复文档
                if doc1.get('file_hash') == doc2.get('file_hash'):
                    continue
                
                content1 = doc1.get('content_preview', '')
                content2 = doc2.get('content_preview', '')
                
                if not content1 or not content2:
                    continue
                
                # 计算内容相似度
                similarity = self._calculate_content_similarity(content1, content2)
                
                if similarity >= self.similarity_threshold:
                    match = DuplicateMatch(
                        document1_path=doc1['file_path'],
                        document2_path=doc2['file_path'],
                        similarity_score=similarity,
                        similarity_type='content',
                        details={
                            'content_length1': len(content1),
                            'content_length2': len(content2)
                        }
                    )
                    duplicates.append(match)
        
        logger.info(f"内容检测发现 {len(duplicates)} 对相似文档")
        return duplicates
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单版本）"""
        if not text1 or not text2:
            return 0.0
        
        # 标准化文本
        text1 = self._normalize_text(text1)
        text2 = self._normalize_text(text2)
        
        # 完全相同
        if text1 == text2:
            return 1.0
        
        # 计算编辑距离相似度
        return self._levenshtein_similarity(text1, text2)
    
    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """计算内容相似度"""
        if not content1 or not content2:
            return 0.0
        
        # 提取关键特征
        features1 = self._extract_content_features(content1)
        features2 = self._extract_content_features(content2)
        
        # 计算特征相似度
        return self._calculate_feature_similarity(features1, features2)
    
    def _normalize_text(self, text: str) -> str:
        """标准化文本"""
        # 转换为小写
        text = text.lower()
        
        # 移除标点符号和多余空格
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _levenshtein_similarity(self, s1: str, s2: str) -> float:
        """计算编辑距离相似度"""
        if len(s1) == 0:
            return 0.0 if len(s2) > 0 else 1.0
        if len(s2) == 0:
            return 0.0
        
        # 简化版编辑距离计算
        max_len = max(len(s1), len(s2))
        
        # 计算公共子序列长度
        common_chars = 0
        for char in set(s1):
            common_chars += min(s1.count(char), s2.count(char))
        
        similarity = common_chars / max_len
        return min(similarity, 1.0)
    
    def _extract_content_features(self, content: str) -> Dict:
        """提取内容特征"""
        features = {}
        
        # 长度特征
        features['length'] = len(content)
        features['word_count'] = len(content.split())
        
        # 字符特征
        features['chinese_chars'] = len(re.findall(r'[\u4e00-\u9fff]', content))
        features['english_chars'] = len(re.findall(r'[a-zA-Z]', content))
        features['digits'] = len(re.findall(r'\d', content))
        
        # 结构特征
        features['lines'] = content.count('\n')
        features['paragraphs'] = len([p for p in content.split('\n\n') if p.strip()])
        
        # 关键词特征（简单版）
        words = re.findall(r'[\u4e00-\u9fff]{2,}|[a-zA-Z]{3,}', content.lower())
        word_freq = {}
        for word in words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # 取前10个高频词
        top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        features['top_words'] = dict(top_words)
        
        return features
    
    def _calculate_feature_similarity(self, features1: Dict, features2: Dict) -> float:
        """计算特征相似度"""
        similarities = []
        
        # 长度相似度
        len1, len2 = features1['length'], features2['length']
        if len1 > 0 and len2 > 0:
            len_sim = min(len1, len2) / max(len1, len2)
            similarities.append(len_sim)
        
        # 字符比例相似度
        for char_type in ['chinese_chars', 'english_chars', 'digits']:
            ratio1 = features1[char_type] / max(features1['length'], 1)
            ratio2 = features2[char_type] / max(features2['length'], 1)
            char_sim = 1 - abs(ratio1 - ratio2)
            similarities.append(char_sim)
        
        # 关键词相似度
        words1 = set(features1['top_words'].keys())
        words2 = set(features2['top_words'].keys())
        if words1 or words2:
            word_sim = len(words1 & words2) / len(words1 | words2)
            similarities.append(word_sim * 2)  # 关键词相似度权重更高
        
        # 计算加权平均
        if similarities:
            return sum(similarities) / len(similarities)
        else:
            return 0.0
    
    def _remove_duplicate_matches(self, matches: List[DuplicateMatch]) -> List[DuplicateMatch]:
        """移除重复的匹配结果"""
        unique_matches = []
        seen_pairs = set()
        
        for match in matches:
            # 创建标准化的文档对标识
            pair = tuple(sorted([match.document1_path, match.document2_path]))
            
            if pair not in seen_pairs:
                seen_pairs.add(pair)
                unique_matches.append(match)
        
        return unique_matches
    
    def group_duplicates(self, matches: List[DuplicateMatch]) -> List[List[str]]:
        """将重复文档分组"""
        # 构建图
        graph = {}
        for match in matches:
            doc1, doc2 = match.document1_path, match.document2_path
            
            if doc1 not in graph:
                graph[doc1] = set()
            if doc2 not in graph:
                graph[doc2] = set()
            
            graph[doc1].add(doc2)
            graph[doc2].add(doc1)
        
        # 找连通分量
        visited = set()
        groups = []
        
        for doc in graph:
            if doc not in visited:
                group = []
                self._dfs(graph, doc, visited, group)
                if len(group) > 1:
                    groups.append(group)
        
        return groups
    
    def _dfs(self, graph: Dict, node: str, visited: set, group: List[str]):
        """深度优先搜索"""
        visited.add(node)
        group.append(node)
        
        for neighbor in graph.get(node, []):
            if neighbor not in visited:
                self._dfs(graph, neighbor, visited, group)


# 全局重复检测器实例
duplicate_detector = DuplicateDetector()
