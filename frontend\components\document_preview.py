"""
文档预览组件
Document Preview Component

提供增强的文档预览功能
"""

import re
from typing import Optional, List, Dict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QLabel, QPushButton,
    QScrollArea, QFrame, QSplitter, QTabWidget, QTreeWidget, QTreeWidgetItem,
    QGroupBox, QLineEdit, QCheckBox, QSpinBox, QComboBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QTextCharFormat, QColor, QTextCursor, QSyntaxHighlighter, QTextDocument

from backend.models.document import Document


class KeywordHighlighter(QSyntaxHighlighter):
    """关键词高亮器"""
    
    def __init__(self, parent: QTextDocument):
        super().__init__(parent)
        self.keywords: List[str] = []
        self.highlight_format = QTextCharFormat()
        self.highlight_format.setBackground(QColor("#ffeb3b"))
        self.highlight_format.setForeground(QColor("#000000"))
    
    def set_keywords(self, keywords: List[str]):
        """设置要高亮的关键词"""
        self.keywords = keywords
        self.rehighlight()
    
    def highlightBlock(self, text: str):
        """高亮文本块"""
        for keyword in self.keywords:
            if not keyword.strip():
                continue
            
            # 使用正则表达式进行不区分大小写的匹配
            pattern = re.compile(re.escape(keyword), re.IGNORECASE)
            for match in pattern.finditer(text):
                start = match.start()
                length = match.end() - start
                self.setFormat(start, length, self.highlight_format)


class DocumentPreviewWidget(QWidget):
    """文档预览组件"""
    
    # 信号定义
    keyword_clicked = Signal(str)  # 关键词点击信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 当前文档
        self.current_document: Optional[Document] = None
        
        # 搜索相关
        self.search_keywords: List[str] = []
        self.current_search_index = 0
        self.search_results: List[int] = []
        
        # 初始化界面
        self.init_ui()
        self.init_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 内容预览标签页
        self.create_content_tab()
        
        # 元数据标签页
        self.create_metadata_tab()
        
        # 关键词标签页
        self.create_keywords_tab()
        
        # 统计信息标签页
        self.create_statistics_tab()
    
    def create_content_tab(self):
        """创建内容预览标签页"""
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        
        # 搜索工具栏
        search_toolbar = self.create_search_toolbar()
        layout.addWidget(search_toolbar)
        
        # 内容显示区域
        self.content_text = QTextEdit()
        self.content_text.setReadOnly(True)
        self.content_text.setFont(QFont("Consolas", 10))
        
        # 设置语法高亮器
        self.highlighter = KeywordHighlighter(self.content_text.document())
        
        layout.addWidget(self.content_text)
        
        # 设置样式
        self.content_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #ddd;
                padding: 10px;
                line-height: 1.5;
            }
        """)
        
        self.tab_widget.addTab(content_widget, "内容预览")
    
    def create_search_toolbar(self) -> QWidget:
        """创建搜索工具栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 搜索框
        layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键词搜索...")
        layout.addWidget(self.search_edit)
        
        # 搜索按钮
        self.search_btn = QPushButton("搜索")
        self.search_btn.setMaximumWidth(80)
        layout.addWidget(self.search_btn)
        
        # 导航按钮
        self.prev_btn = QPushButton("上一个")
        self.prev_btn.setMaximumWidth(80)
        self.prev_btn.setEnabled(False)
        layout.addWidget(self.prev_btn)
        
        self.next_btn = QPushButton("下一个")
        self.next_btn.setMaximumWidth(80)
        self.next_btn.setEnabled(False)
        layout.addWidget(self.next_btn)
        
        # 搜索结果标签
        self.search_result_label = QLabel("")
        layout.addWidget(self.search_result_label)
        
        layout.addStretch()
        
        # 选项
        self.case_sensitive_cb = QCheckBox("区分大小写")
        layout.addWidget(self.case_sensitive_cb)
        
        self.whole_word_cb = QCheckBox("全词匹配")
        layout.addWidget(self.whole_word_cb)
        
        return toolbar
    
    def create_metadata_tab(self):
        """创建元数据标签页"""
        metadata_widget = QScrollArea()
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QVBoxLayout(basic_group)
        
        self.metadata_labels = {}
        basic_fields = [
            ("文件名", "file_name"),
            ("文件路径", "file_path"),
            ("文件大小", "file_size"),
            ("文件类型", "detected_type"),
            ("MIME类型", "mime_type"),
            ("文件哈希", "file_hash"),
            ("创建时间", "created_at"),
            ("修改时间", "updated_at")
        ]
        
        for label_text, field_name in basic_fields:
            label = QLabel(f"{label_text}: 未选择文档")
            label.setWordWrap(True)
            self.metadata_labels[field_name] = label
            basic_layout.addWidget(label)
        
        layout.addWidget(basic_group)
        
        # 文档信息组
        doc_group = QGroupBox("文档信息")
        doc_layout = QVBoxLayout(doc_group)
        
        doc_fields = [
            ("标题", "title"),
            ("作者", "author"),
            ("出版社", "publisher"),
            ("出版日期", "publish_date"),
            ("文档类型", "document_type"),
            ("语言", "language"),
            ("页数", "page_count"),
            ("字数", "word_count")
        ]
        
        for label_text, field_name in doc_fields:
            label = QLabel(f"{label_text}: 未选择文档")
            label.setWordWrap(True)
            self.metadata_labels[field_name] = label
            doc_layout.addWidget(label)
        
        layout.addWidget(doc_group)
        
        # 处理信息组
        process_group = QGroupBox("处理信息")
        process_layout = QVBoxLayout(process_group)
        
        process_fields = [
            ("处理状态", "is_processed"),
            ("处理错误", "processing_error"),
            ("最后访问", "last_accessed")
        ]
        
        for label_text, field_name in process_fields:
            label = QLabel(f"{label_text}: 未选择文档")
            label.setWordWrap(True)
            self.metadata_labels[field_name] = label
            process_layout.addWidget(label)
        
        layout.addWidget(process_group)
        layout.addStretch()
        
        metadata_widget.setWidget(content_widget)
        self.tab_widget.addTab(metadata_widget, "元数据")
    
    def create_keywords_tab(self):
        """创建关键词标签页"""
        keywords_widget = QWidget()
        layout = QVBoxLayout(keywords_widget)
        
        # 关键词工具栏
        toolbar_layout = QHBoxLayout()
        
        toolbar_layout.addWidget(QLabel("显示:"))
        self.keyword_filter_combo = QComboBox()
        self.keyword_filter_combo.addItems(["全部关键词", "高频关键词", "低频关键词"])
        toolbar_layout.addWidget(self.keyword_filter_combo)
        
        toolbar_layout.addWidget(QLabel("最少频次:"))
        self.min_frequency_spin = QSpinBox()
        self.min_frequency_spin.setRange(1, 100)
        self.min_frequency_spin.setValue(1)
        toolbar_layout.addWidget(self.min_frequency_spin)
        
        toolbar_layout.addStretch()
        
        self.highlight_keywords_btn = QPushButton("高亮显示")
        toolbar_layout.addWidget(self.highlight_keywords_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 关键词列表
        self.keywords_tree = QTreeWidget()
        self.keywords_tree.setHeaderLabels(["关键词", "频次", "权重"])
        self.keywords_tree.setRootIsDecorated(False)
        self.keywords_tree.setAlternatingRowColors(True)
        layout.addWidget(self.keywords_tree)
        
        self.tab_widget.addTab(keywords_widget, "关键词")
    
    def create_statistics_tab(self):
        """创建统计信息标签页"""
        stats_widget = QWidget()
        layout = QVBoxLayout(stats_widget)
        
        # 文本统计组
        text_group = QGroupBox("文本统计")
        text_layout = QVBoxLayout(text_group)
        
        self.stats_labels = {}
        stats_fields = [
            ("总字符数", "total_chars"),
            ("总字数", "total_words"),
            ("总行数", "total_lines"),
            ("段落数", "total_paragraphs"),
            ("中文字符数", "chinese_chars"),
            ("英文字符数", "english_chars"),
            ("数字字符数", "digit_chars"),
            ("标点符号数", "punctuation_chars")
        ]
        
        for label_text, field_name in stats_fields:
            label = QLabel(f"{label_text}: 0")
            self.stats_labels[field_name] = label
            text_layout.addWidget(label)
        
        layout.addWidget(text_group)
        layout.addStretch()
        
        self.tab_widget.addTab(stats_widget, "统计")
    
    def init_connections(self):
        """初始化信号连接"""
        # 搜索相关
        self.search_btn.clicked.connect(self.search_in_content)
        self.search_edit.returnPressed.connect(self.search_in_content)
        self.prev_btn.clicked.connect(self.find_previous)
        self.next_btn.clicked.connect(self.find_next)
        
        # 关键词相关
        self.highlight_keywords_btn.clicked.connect(self.highlight_keywords)
        self.keywords_tree.itemDoubleClicked.connect(self.on_keyword_double_clicked)
        self.keyword_filter_combo.currentTextChanged.connect(self.filter_keywords)
        self.min_frequency_spin.valueChanged.connect(self.filter_keywords)
    
    def load_document(self, document: Document):
        """加载文档"""
        self.current_document = document
        
        if document:
            # 更新内容预览
            self.update_content_preview(document)
            
            # 更新元数据
            self.update_metadata(document)
            
            # 更新关键词
            self.update_keywords(document)
            
            # 更新统计信息
            self.update_statistics(document)
        else:
            self.clear_preview()
    
    def update_content_preview(self, document: Document):
        """更新内容预览"""
        if document.content_preview:
            self.content_text.setPlainText(document.content_preview)
        else:
            self.content_text.setPlainText("暂无预览内容")
    
    def update_metadata(self, document: Document):
        """更新元数据"""
        # 基本信息
        self.metadata_labels["file_name"].setText(f"文件名: {document.file_name}")
        self.metadata_labels["file_path"].setText(f"文件路径: {document.file_path}")
        self.metadata_labels["file_size"].setText(f"文件大小: {self.format_file_size(document.file_size)}")
        self.metadata_labels["detected_type"].setText(f"文件类型: {document.detected_type}")
        self.metadata_labels["mime_type"].setText(f"MIME类型: {document.mime_type or '未知'}")
        self.metadata_labels["file_hash"].setText(f"文件哈希: {document.file_hash}")
        self.metadata_labels["created_at"].setText(f"创建时间: {document.created_at.strftime('%Y-%m-%d %H:%M:%S') if document.created_at else '未知'}")
        self.metadata_labels["updated_at"].setText(f"修改时间: {document.updated_at.strftime('%Y-%m-%d %H:%M:%S') if document.updated_at else '未知'}")
        
        # 文档信息
        self.metadata_labels["title"].setText(f"标题: {document.title or '未知'}")
        self.metadata_labels["author"].setText(f"作者: {document.author or '未知'}")
        self.metadata_labels["publisher"].setText(f"出版社: {document.publisher or '未知'}")
        self.metadata_labels["publish_date"].setText(f"出版日期: {document.publish_date or '未知'}")
        self.metadata_labels["document_type"].setText(f"文档类型: {document.document_type or '未知'}")
        self.metadata_labels["language"].setText(f"语言: {self.format_language(document.language)}")
        self.metadata_labels["page_count"].setText(f"页数: {document.page_count or '未知'}")
        self.metadata_labels["word_count"].setText(f"字数: {document.word_count or '未知'}")
        
        # 处理信息
        self.metadata_labels["is_processed"].setText(f"处理状态: {'已处理' if document.is_processed else '未处理'}")
        self.metadata_labels["processing_error"].setText(f"处理错误: {document.processing_error or '无'}")
        self.metadata_labels["last_accessed"].setText(f"最后访问: {document.last_accessed.strftime('%Y-%m-%d %H:%M:%S') if document.last_accessed else '未访问'}")
    
    def update_keywords(self, document: Document):
        """更新关键词"""
        self.keywords_tree.clear()
        
        if document.keywords:
            for keyword in document.keywords:
                item = QTreeWidgetItem([
                    keyword.word,
                    str(keyword.frequency),
                    "1.0"  # 权重暂时固定为1.0
                ])
                self.keywords_tree.addTopLevelItem(item)
        
        # 调整列宽
        self.keywords_tree.resizeColumnToContents(0)
        self.keywords_tree.resizeColumnToContents(1)
        self.keywords_tree.resizeColumnToContents(2)
    
    def update_statistics(self, document: Document):
        """更新统计信息"""
        content = document.content_preview or ""
        
        # 计算各种统计信息
        total_chars = len(content)
        total_words = len(content.split())
        total_lines = content.count('\n') + 1 if content else 0
        total_paragraphs = len([p for p in content.split('\n\n') if p.strip()])
        
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', content))
        english_chars = len(re.findall(r'[a-zA-Z]', content))
        digit_chars = len(re.findall(r'\d', content))
        punctuation_chars = len(re.findall(r'[^\w\s\u4e00-\u9fff]', content))
        
        # 更新标签
        self.stats_labels["total_chars"].setText(f"总字符数: {total_chars}")
        self.stats_labels["total_words"].setText(f"总字数: {total_words}")
        self.stats_labels["total_lines"].setText(f"总行数: {total_lines}")
        self.stats_labels["total_paragraphs"].setText(f"段落数: {total_paragraphs}")
        self.stats_labels["chinese_chars"].setText(f"中文字符数: {chinese_chars}")
        self.stats_labels["english_chars"].setText(f"英文字符数: {english_chars}")
        self.stats_labels["digit_chars"].setText(f"数字字符数: {digit_chars}")
        self.stats_labels["punctuation_chars"].setText(f"标点符号数: {punctuation_chars}")
    
    def clear_preview(self):
        """清空预览"""
        self.content_text.clear()
        
        # 清空元数据
        for label in self.metadata_labels.values():
            label.setText(label.text().split(':')[0] + ": 未选择文档")
        
        # 清空关键词
        self.keywords_tree.clear()
        
        # 清空统计
        for label in self.stats_labels.values():
            label.setText(label.text().split(':')[0] + ": 0")
    
    def format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def format_language(self, language: str) -> str:
        """格式化语言显示"""
        if not language:
            return "未知"
        
        lang_map = {
            "zh": "中文",
            "en": "英文",
            "mixed": "中英混合"
        }
        return lang_map.get(language, language)
    
    # === 搜索功能 ===
    
    def search_in_content(self):
        """在内容中搜索"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            return
        
        # 清除之前的搜索结果
        self.search_results.clear()
        self.current_search_index = 0
        
        # 获取文本内容
        content = self.content_text.toPlainText()
        
        # 搜索选项
        case_sensitive = self.case_sensitive_cb.isChecked()
        whole_word = self.whole_word_cb.isChecked()
        
        # 构建搜索模式
        if whole_word:
            pattern = r'\b' + re.escape(keyword) + r'\b'
        else:
            pattern = re.escape(keyword)
        
        flags = 0 if case_sensitive else re.IGNORECASE
        
        # 查找所有匹配
        for match in re.finditer(pattern, content, flags):
            self.search_results.append(match.start())
        
        # 更新搜索结果显示
        if self.search_results:
            self.search_result_label.setText(f"找到 {len(self.search_results)} 个结果")
            self.prev_btn.setEnabled(True)
            self.next_btn.setEnabled(True)
            self.highlight_search_results(keyword)
            self.goto_search_result(0)
        else:
            self.search_result_label.setText("未找到结果")
            self.prev_btn.setEnabled(False)
            self.next_btn.setEnabled(False)
    
    def find_previous(self):
        """查找上一个"""
        if self.search_results and self.current_search_index > 0:
            self.current_search_index -= 1
            self.goto_search_result(self.current_search_index)
    
    def find_next(self):
        """查找下一个"""
        if self.search_results and self.current_search_index < len(self.search_results) - 1:
            self.current_search_index += 1
            self.goto_search_result(self.current_search_index)
    
    def goto_search_result(self, index: int):
        """跳转到搜索结果"""
        if 0 <= index < len(self.search_results):
            position = self.search_results[index]
            cursor = self.content_text.textCursor()
            cursor.setPosition(position)
            self.content_text.setTextCursor(cursor)
            self.content_text.ensureCursorVisible()
            
            # 更新结果显示
            self.search_result_label.setText(f"结果 {index + 1}/{len(self.search_results)}")
    
    def highlight_search_results(self, keyword: str):
        """高亮搜索结果"""
        self.highlighter.set_keywords([keyword])
    
    def highlight_keywords(self):
        """高亮关键词"""
        if not self.current_document or not self.current_document.keywords:
            return
        
        keywords = [kw.word for kw in self.current_document.keywords]
        self.highlighter.set_keywords(keywords)
    
    def on_keyword_double_clicked(self, item: QTreeWidgetItem, column: int):
        """关键词双击事件"""
        keyword = item.text(0)
        self.search_edit.setText(keyword)
        self.search_in_content()
        self.keyword_clicked.emit(keyword)
    
    def filter_keywords(self):
        """过滤关键词显示"""
        if not self.current_document:
            return
        
        filter_type = self.keyword_filter_combo.currentText()
        min_frequency = self.min_frequency_spin.value()
        
        # 重新加载关键词
        self.keywords_tree.clear()
        
        if self.current_document.keywords:
            for keyword in self.current_document.keywords:
                if keyword.frequency < min_frequency:
                    continue
                
                if filter_type == "高频关键词" and keyword.frequency < 5:
                    continue
                elif filter_type == "低频关键词" and keyword.frequency >= 5:
                    continue
                
                item = QTreeWidgetItem([
                    keyword.word,
                    str(keyword.frequency),
                    "1.0"
                ])
                self.keywords_tree.addTopLevelItem(item)
