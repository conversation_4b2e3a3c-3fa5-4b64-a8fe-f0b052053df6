[tool.poetry]
name = "ikonw"
version = "0.1.0"
description = "Personal Document Management System - 个人文档管理系统"
authors = ["sha <<EMAIL>>"]
readme = "README.md"
packages = [{include = "backend"}, {include = "frontend"}]

[tool.poetry.dependencies]
python = "^3.12"
# GUI框架
pyside6 = "^6.8.0"
# 数据库
sqlalchemy = "^2.0.0"
# 文档解析
pypdf2 = "^3.0.0"
python-docx = "^1.1.0"
markdown = "^3.6"
openpyxl = "^3.1.0"
# NLP和文本处理
jieba = "^0.42.1"
nltk = "^3.9"
scikit-learn = "^1.5.0"
# 文件类型检测
python-magic = "^0.4.27"
# 图像处理（用于PDF等）
pillow = "^10.4.0"
# 配置管理
pydantic = "^2.9.0"
pydantic-settings = "^2.6.0"
# 日志
loguru = "^0.7.2"
# 进度条
tqdm = "^4.66.0"
# 多语言支持
babel = "^2.16.0"
# 文本相似度
fuzzywuzzy = "^0.18.0"
python-levenshtein = "^0.25.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.0"
pytest-qt = "^4.4.0"
pytest-cov = "^5.0.0"
black = "^24.8.0"
isort = "^5.13.0"
flake8 = "^7.1.0"
mypy = "^1.11.0"
pre-commit = "^4.0.0"

[tool.poetry.group.build.dependencies]
pyinstaller = "^6.10.0"
cx-freeze = "^7.2.0"
nuitka = "^2.4.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
ikonw = "frontend.main:main"

# 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=backend --cov=frontend --cov-report=html --cov-report=term-missing"
