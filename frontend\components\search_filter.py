"""
搜索和过滤组件
Search and Filter Component

提供高级搜索和过滤功能
"""

from typing import Dict, Any, List, Callable
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLineEdit, QComboBox,
    QPushButton, QCheckBox, QDateEdit, QSpinBox, QLabel, QSlider,
    QButtonGroup, QRadioButton, QFrame
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont

from backend.models.document import Document


class SearchFilterWidget(QWidget):
    """搜索和过滤组件"""
    
    # 信号定义
    search_requested = Signal(dict)  # 搜索请求信号，传递搜索条件字典
    filter_changed = Signal(dict)    # 过滤条件变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 搜索条件
        self.search_criteria = {}
        
        # 初始化界面
        self.init_ui()
        self.init_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 基本搜索组
        self.create_basic_search_group(layout)
        
        # 高级过滤组
        self.create_advanced_filter_group(layout)
        
        # 操作按钮组
        self.create_action_buttons(layout)
        
        # 设置样式
        self.set_style()
    
    def create_basic_search_group(self, parent_layout):
        """创建基本搜索组"""
        group = QGroupBox("基本搜索")
        group.setFont(QFont("", 10, QFont.Bold))
        layout = QVBoxLayout(group)
        
        # 关键词搜索
        keyword_layout = QHBoxLayout()
        keyword_layout.addWidget(QLabel("关键词:"))
        
        self.keyword_edit = QLineEdit()
        self.keyword_edit.setPlaceholderText("输入标题、作者、内容关键词...")
        keyword_layout.addWidget(self.keyword_edit)
        
        self.search_btn = QPushButton("搜索")
        self.search_btn.setMinimumWidth(80)
        keyword_layout.addWidget(self.search_btn)
        
        layout.addLayout(keyword_layout)
        
        # 搜索范围选择
        scope_layout = QHBoxLayout()
        scope_layout.addWidget(QLabel("搜索范围:"))
        
        self.scope_group = QButtonGroup()
        self.scope_all = QRadioButton("全部")
        self.scope_title = QRadioButton("标题")
        self.scope_author = QRadioButton("作者")
        self.scope_content = QRadioButton("内容")
        
        self.scope_all.setChecked(True)
        
        self.scope_group.addButton(self.scope_all, 0)
        self.scope_group.addButton(self.scope_title, 1)
        self.scope_group.addButton(self.scope_author, 2)
        self.scope_group.addButton(self.scope_content, 3)
        
        scope_layout.addWidget(self.scope_all)
        scope_layout.addWidget(self.scope_title)
        scope_layout.addWidget(self.scope_author)
        scope_layout.addWidget(self.scope_content)
        scope_layout.addStretch()
        
        layout.addLayout(scope_layout)
        
        parent_layout.addWidget(group)
    
    def create_advanced_filter_group(self, parent_layout):
        """创建高级过滤组"""
        group = QGroupBox("高级过滤")
        group.setFont(QFont("", 10, QFont.Bold))
        layout = QVBoxLayout(group)
        
        # 第一行：文档类型和语言
        row1_layout = QHBoxLayout()
        
        # 文档类型
        row1_layout.addWidget(QLabel("文档类型:"))
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "全部", "书籍", "论文", "报告", "总结", "手册", "其他"
        ])
        row1_layout.addWidget(self.type_combo)
        
        # 语言
        row1_layout.addWidget(QLabel("语言:"))
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            "全部", "中文", "英文", "中英混合"
        ])
        row1_layout.addWidget(self.language_combo)
        
        row1_layout.addStretch()
        layout.addLayout(row1_layout)
        
        # 第二行：文件大小范围
        row2_layout = QHBoxLayout()
        row2_layout.addWidget(QLabel("文件大小:"))
        
        self.size_min_spin = QSpinBox()
        self.size_min_spin.setRange(0, 999999)
        self.size_min_spin.setSuffix(" KB")
        row2_layout.addWidget(self.size_min_spin)
        
        row2_layout.addWidget(QLabel("到"))
        
        self.size_max_spin = QSpinBox()
        self.size_max_spin.setRange(0, 999999)
        self.size_max_spin.setValue(999999)
        self.size_max_spin.setSuffix(" KB")
        row2_layout.addWidget(self.size_max_spin)
        
        row2_layout.addStretch()
        layout.addLayout(row2_layout)
        
        # 第三行：页数和字数范围
        row3_layout = QHBoxLayout()
        
        # 页数范围
        row3_layout.addWidget(QLabel("页数:"))
        self.pages_min_spin = QSpinBox()
        self.pages_min_spin.setRange(0, 9999)
        row3_layout.addWidget(self.pages_min_spin)
        
        row3_layout.addWidget(QLabel("到"))
        
        self.pages_max_spin = QSpinBox()
        self.pages_max_spin.setRange(0, 9999)
        self.pages_max_spin.setValue(9999)
        row3_layout.addWidget(self.pages_max_spin)
        
        # 字数范围
        row3_layout.addWidget(QLabel("字数:"))
        self.words_min_spin = QSpinBox()
        self.words_min_spin.setRange(0, 999999)
        row3_layout.addWidget(self.words_min_spin)
        
        row3_layout.addWidget(QLabel("到"))
        
        self.words_max_spin = QSpinBox()
        self.words_max_spin.setRange(0, 999999)
        self.words_max_spin.setValue(999999)
        row3_layout.addWidget(self.words_max_spin)
        
        row3_layout.addStretch()
        layout.addLayout(row3_layout)
        
        # 第四行：日期范围
        row4_layout = QHBoxLayout()
        row4_layout.addWidget(QLabel("修改日期:"))
        
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addYears(-1))
        self.date_from.setCalendarPopup(True)
        row4_layout.addWidget(self.date_from)
        
        row4_layout.addWidget(QLabel("到"))
        
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        row4_layout.addWidget(self.date_to)
        
        row4_layout.addStretch()
        layout.addLayout(row4_layout)
        
        # 第五行：状态过滤
        row5_layout = QHBoxLayout()
        
        self.processed_only = QCheckBox("仅显示已处理文档")
        row5_layout.addWidget(self.processed_only)
        
        self.has_keywords = QCheckBox("仅显示有关键词的文档")
        row5_layout.addWidget(self.has_keywords)
        
        row5_layout.addStretch()
        layout.addLayout(row5_layout)
        
        parent_layout.addWidget(group)
    
    def create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        
        self.apply_filter_btn = QPushButton("应用过滤")
        self.apply_filter_btn.setMinimumWidth(100)
        button_layout.addWidget(self.apply_filter_btn)
        
        self.clear_filter_btn = QPushButton("清除过滤")
        self.clear_filter_btn.setMinimumWidth(100)
        button_layout.addWidget(self.clear_filter_btn)
        
        button_layout.addStretch()
        
        self.save_criteria_btn = QPushButton("保存条件")
        self.save_criteria_btn.setMinimumWidth(100)
        button_layout.addWidget(self.save_criteria_btn)
        
        self.load_criteria_btn = QPushButton("加载条件")
        self.load_criteria_btn.setMinimumWidth(100)
        button_layout.addWidget(self.load_criteria_btn)
        
        parent_layout.addLayout(button_layout)
    
    def init_connections(self):
        """初始化信号连接"""
        # 搜索按钮
        self.search_btn.clicked.connect(self.perform_search)
        self.keyword_edit.returnPressed.connect(self.perform_search)
        
        # 过滤按钮
        self.apply_filter_btn.clicked.connect(self.apply_filters)
        self.clear_filter_btn.clicked.connect(self.clear_filters)
        
        # 保存/加载条件
        self.save_criteria_btn.clicked.connect(self.save_search_criteria)
        self.load_criteria_btn.clicked.connect(self.load_search_criteria)
        
        # 实时过滤（可选）
        self.type_combo.currentTextChanged.connect(self.on_filter_changed)
        self.language_combo.currentTextChanged.connect(self.on_filter_changed)
    
    def perform_search(self):
        """执行搜索"""
        criteria = self.get_search_criteria()
        self.search_requested.emit(criteria)
    
    def apply_filters(self):
        """应用过滤条件"""
        criteria = self.get_filter_criteria()
        self.filter_changed.emit(criteria)
    
    def clear_filters(self):
        """清除所有过滤条件"""
        # 重置所有控件
        self.keyword_edit.clear()
        self.scope_all.setChecked(True)
        self.type_combo.setCurrentIndex(0)
        self.language_combo.setCurrentIndex(0)
        
        self.size_min_spin.setValue(0)
        self.size_max_spin.setValue(999999)
        self.pages_min_spin.setValue(0)
        self.pages_max_spin.setValue(9999)
        self.words_min_spin.setValue(0)
        self.words_max_spin.setValue(999999)
        
        self.date_from.setDate(QDate.currentDate().addYears(-1))
        self.date_to.setDate(QDate.currentDate())
        
        self.processed_only.setChecked(False)
        self.has_keywords.setChecked(False)
        
        # 发送清空信号
        self.filter_changed.emit({})
    
    def get_search_criteria(self) -> Dict[str, Any]:
        """获取搜索条件"""
        criteria = {
            "keyword": self.keyword_edit.text().strip(),
            "scope": self.scope_group.checkedId()  # 0:全部, 1:标题, 2:作者, 3:内容
        }
        return criteria
    
    def get_filter_criteria(self) -> Dict[str, Any]:
        """获取过滤条件"""
        criteria = {}
        
        # 基本搜索
        if self.keyword_edit.text().strip():
            criteria["keyword"] = self.keyword_edit.text().strip()
            criteria["scope"] = self.scope_group.checkedId()
        
        # 文档类型
        if self.type_combo.currentText() != "全部":
            criteria["document_type"] = self.type_combo.currentText()
        
        # 语言
        if self.language_combo.currentText() != "全部":
            lang_map = {"中文": "zh", "英文": "en", "中英混合": "mixed"}
            criteria["language"] = lang_map.get(self.language_combo.currentText())
        
        # 文件大小范围
        if self.size_min_spin.value() > 0 or self.size_max_spin.value() < 999999:
            criteria["file_size_range"] = (
                self.size_min_spin.value() * 1024,  # 转换为字节
                self.size_max_spin.value() * 1024
            )
        
        # 页数范围
        if self.pages_min_spin.value() > 0 or self.pages_max_spin.value() < 9999:
            criteria["page_count_range"] = (
                self.pages_min_spin.value(),
                self.pages_max_spin.value()
            )
        
        # 字数范围
        if self.words_min_spin.value() > 0 or self.words_max_spin.value() < 999999:
            criteria["word_count_range"] = (
                self.words_min_spin.value(),
                self.words_max_spin.value()
            )
        
        # 日期范围
        criteria["date_range"] = (
            self.date_from.date().toPython(),
            self.date_to.date().toPython()
        )
        
        # 状态过滤
        if self.processed_only.isChecked():
            criteria["processed_only"] = True
        
        if self.has_keywords.isChecked():
            criteria["has_keywords"] = True
        
        return criteria
    
    def on_filter_changed(self):
        """过滤条件变化事件"""
        # 可以实现实时过滤
        pass
    
    def save_search_criteria(self):
        """保存搜索条件"""
        # 这里可以实现保存到配置文件的功能
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, "提示", "保存搜索条件功能开发中...")
    
    def load_search_criteria(self):
        """加载搜索条件"""
        # 这里可以实现从配置文件加载的功能
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, "提示", "加载搜索条件功能开发中...")
    
    def set_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 5px;
                margin: 10px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #2196f3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
            QLineEdit, QComboBox, QSpinBox, QDateEdit {
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 10pt;
            }
            QCheckBox, QRadioButton {
                font-size: 10pt;
            }
        """)
    
    def create_filter_function(self, criteria: Dict[str, Any]) -> Callable[[Document], bool]:
        """根据条件创建过滤函数"""
        def filter_func(document: Document) -> bool:
            # 关键词搜索
            if "keyword" in criteria and criteria["keyword"]:
                keyword = criteria["keyword"].lower()
                scope = criteria.get("scope", 0)
                
                if scope == 1:  # 标题
                    if keyword not in (document.title or "").lower():
                        return False
                elif scope == 2:  # 作者
                    if keyword not in (document.author or "").lower():
                        return False
                elif scope == 3:  # 内容
                    if keyword not in (document.content_preview or "").lower():
                        return False
                else:  # 全部
                    if not any(keyword in (field or "").lower() for field in [
                        document.title, document.author, document.content_preview, document.file_name
                    ]):
                        return False
            
            # 文档类型
            if "document_type" in criteria:
                if document.document_type != criteria["document_type"]:
                    return False
            
            # 语言
            if "language" in criteria:
                if document.language != criteria["language"]:
                    return False
            
            # 文件大小范围
            if "file_size_range" in criteria:
                min_size, max_size = criteria["file_size_range"]
                if not (min_size <= document.file_size <= max_size):
                    return False
            
            # 页数范围
            if "page_count_range" in criteria and document.page_count:
                min_pages, max_pages = criteria["page_count_range"]
                if not (min_pages <= document.page_count <= max_pages):
                    return False
            
            # 字数范围
            if "word_count_range" in criteria and document.word_count:
                min_words, max_words = criteria["word_count_range"]
                if not (min_words <= document.word_count <= max_words):
                    return False
            
            # 日期范围
            if "date_range" in criteria and document.updated_at:
                date_from, date_to = criteria["date_range"]
                doc_date = document.updated_at.date()
                if not (date_from <= doc_date <= date_to):
                    return False
            
            # 状态过滤
            if criteria.get("processed_only", False):
                if not document.is_processed:
                    return False
            
            if criteria.get("has_keywords", False):
                if not document.keywords:
                    return False
            
            return True
        
        return filter_func
