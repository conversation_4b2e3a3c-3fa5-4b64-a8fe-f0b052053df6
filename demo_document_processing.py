"""
文档处理功能演示
Document Processing Demo

演示文档解析、分析和处理功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 简化版导入，避免依赖问题
try:
    from backend.core.document_parser import DocumentParserManager, DocumentParseResult
    from backend.core.document_analyzer import DocumentAnalyzer, DocumentInfo
    FULL_FEATURES = True
except ImportError:
    FULL_FEATURES = False
    print("注意：部分功能需要安装依赖包才能使用")


class SimpleDocumentDemo:
    """简化版文档处理演示"""
    
    def __init__(self):
        if FULL_FEATURES:
            self.parser = DocumentParserManager()
            self.analyzer = DocumentAnalyzer()
        
    def create_sample_documents(self):
        """创建示例文档"""
        samples_dir = Path("samples")
        samples_dir.mkdir(exist_ok=True)
        
        # 创建示例Markdown文档
        md_content = """# 人工智能技术发展报告

作者：张三
出版社：科技出版社
出版日期：2024年

## 摘要

本报告分析了人工智能技术的最新发展趋势，包括机器学习、深度学习、自然语言处理等关键技术领域。

## 第一章 引言

人工智能（Artificial Intelligence, AI）是计算机科学的一个重要分支，旨在创建能够执行通常需要人类智能的任务的系统。

### 1.1 发展历史

人工智能的概念最早可以追溯到1950年代，当时艾伦·图灵提出了著名的图灵测试。

### 1.2 主要技术

- 机器学习 (Machine Learning)
- 深度学习 (Deep Learning)  
- 自然语言处理 (Natural Language Processing)
- 计算机视觉 (Computer Vision)

## 第二章 技术分析

### 2.1 机器学习

机器学习是人工智能的核心技术之一，通过算法让计算机从数据中学习模式。

### 2.2 深度学习

深度学习基于人工神经网络，特别是深层神经网络，在图像识别、语音识别等领域取得了突破性进展。

## 第三章 应用领域

人工智能技术已经在多个领域得到广泛应用：

1. 医疗健康
2. 金融服务
3. 自动驾驶
4. 智能制造
5. 教育培训

## 结论

人工智能技术正在快速发展，将对社会各个方面产生深远影响。我们需要在推动技术进步的同时，关注其带来的伦理和社会问题。

## 参考文献

1. Russell, S., & Norvig, P. (2020). Artificial Intelligence: A Modern Approach.
2. Goodfellow, I., Bengio, Y., & Courville, A. (2016). Deep Learning.
3. 李航. (2019). 统计学习方法. 清华大学出版社.

---
关键词：人工智能, 机器学习, 深度学习, 自然语言处理, 计算机视觉
"""
        
        md_file = samples_dir / "ai_report.md"
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        # 创建示例文本文档
        txt_content = """机器学习入门指南

作者：李四
时间：2024年6月

什么是机器学习？

机器学习是人工智能的一个子领域，它使计算机能够在没有明确编程的情况下学习和改进。

主要类型：

1. 监督学习
   - 分类问题
   - 回归问题

2. 无监督学习
   - 聚类分析
   - 降维技术

3. 强化学习
   - 奖励机制
   - 策略优化

常用算法：

- 线性回归
- 决策树
- 随机森林
- 支持向量机
- 神经网络

实际应用：

机器学习在现实生活中有很多应用，比如推荐系统、图像识别、语音助手等。

学习建议：

1. 掌握数学基础（线性代数、概率论、统计学）
2. 学习编程语言（Python、R）
3. 实践项目经验
4. 关注最新研究动态

总结：

机器学习是一个快速发展的领域，需要持续学习和实践。
"""
        
        txt_file = samples_dir / "ml_guide.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(txt_content)
        
        print(f"示例文档已创建在 {samples_dir} 目录下")
        return [md_file, txt_file]
    
    def demo_basic_parsing(self, file_path: Path):
        """演示基本解析功能"""
        print(f"\n=== 解析文档: {file_path.name} ===")
        
        if not FULL_FEATURES:
            # 简化版解析
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"文件大小: {len(content)} 字符")
                print(f"内容预览: {content[:200]}...")
                
                # 简单的字数统计
                chinese_chars = len([c for c in content if '\u4e00' <= c <= '\u9fff'])
                english_words = len(content.split())
                print(f"中文字符数: {chinese_chars}")
                print(f"英文单词数: {english_words}")
                
            except Exception as e:
                print(f"解析失败: {e}")
            return
        
        # 完整功能解析
        try:
            result = self.parser.parse_document(file_path)
            
            print(f"解析状态: {'成功' if result.success else '失败'}")
            if not result.success:
                print(f"错误信息: {result.error_message}")
                return
            
            print(f"内容长度: {len(result.content)} 字符")
            print(f"字数统计: {result.word_count}")
            print(f"页数估算: {result.page_count}")
            print(f"检测语言: {result.language}")
            print(f"文件编码: {result.encoding}")
            
            if result.metadata:
                print("元数据:")
                for key, value in result.metadata.items():
                    print(f"  {key}: {value}")
            
            print(f"内容预览:\n{result.content[:300]}...")
            
        except Exception as e:
            print(f"解析出错: {e}")
    
    def demo_document_analysis(self, file_path: Path):
        """演示文档分析功能"""
        print(f"\n=== 分析文档: {file_path.name} ===")
        
        if not FULL_FEATURES:
            print("需要安装完整依赖包才能使用文档分析功能")
            return
        
        try:
            # 先解析文档
            parse_result = self.parser.parse_document(file_path)
            if not parse_result.success:
                print(f"解析失败: {parse_result.error_message}")
                return
            
            # 分析文档信息
            doc_info = self.analyzer.analyze_document(parse_result, file_path)
            
            print(f"文档标题: {doc_info.title}")
            print(f"文档作者: {doc_info.author}")
            print(f"文档类型: {doc_info.document_type}")
            print(f"出版社: {doc_info.publisher}")
            print(f"出版日期: {doc_info.publish_date}")
            print(f"文档语言: {doc_info.language}")
            print(f"主题领域: {doc_info.subject}")
            
            # 查找目录位置
            toc_position = self.analyzer.find_table_of_contents(parse_result.content)
            if toc_position is not None:
                print(f"目录位置: 第 {toc_position} 个字符")
                
                # 提取目录前内容
                before_toc = self.analyzer.extract_content_before_toc(parse_result.content)
                print(f"目录前内容长度: {len(before_toc)} 字符")
            else:
                print("未找到目录")
            
        except Exception as e:
            print(f"分析出错: {e}")
    
    def demo_performance_test(self, file_paths: list):
        """演示性能测试"""
        print(f"\n=== 性能测试 ===")
        
        total_start = time.time()
        
        for file_path in file_paths:
            start_time = time.time()
            
            if FULL_FEATURES:
                result = self.parser.parse_document(file_path)
                status = "成功" if result.success else "失败"
            else:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    status = "成功"
                except:
                    status = "失败"
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"{file_path.name}: {status} - {processing_time:.3f}秒")
        
        total_time = time.time() - total_start
        print(f"总处理时间: {total_time:.3f}秒")
        print(f"平均处理时间: {total_time/len(file_paths):.3f}秒")


def main():
    """主函数"""
    print("文档处理功能演示")
    print("Document Processing Demo")
    print("=" * 50)
    
    demo = SimpleDocumentDemo()
    
    try:
        # 创建示例文档
        sample_files = demo.create_sample_documents()
        
        # 演示解析功能
        for file_path in sample_files:
            demo.demo_basic_parsing(file_path)
        
        # 演示分析功能
        for file_path in sample_files:
            demo.demo_document_analysis(file_path)
        
        # 性能测试
        demo.demo_performance_test(sample_files)
        
        print("\n" + "=" * 50)
        print("演示完成！")
        
        if not FULL_FEATURES:
            print("\n要体验完整功能，请安装依赖:")
            print("pip install -r requirements.txt")
            print("或者: poetry install")
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
