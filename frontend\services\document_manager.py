"""
文档管理服务
Document Manager Service

前端文档管理的核心服务类，负责与后端服务的交互
"""

import sys
from pathlib import Path
from typing import List, Optional, Dict, Any, Callable
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from PySide6.QtWidgets import QApplication

# 添加后端路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from backend.services.document_service import document_service, DocumentProcessingResult
from backend.models.document import Document
from backend.core.database import db_manager
from backend.core.file_scanner import file_scanner


class DocumentProcessingThread(QThread):
    """文档处理线程"""
    
    # 信号定义
    progress_updated = Signal(int, int, str)  # 当前进度, 总数, 当前文件名
    document_processed = Signal(DocumentProcessingResult)  # 单个文档处理完成
    batch_completed = Signal(list)  # 批量处理完成
    error_occurred = Signal(str)  # 错误发生
    
    def __init__(self, file_paths: List[Path]):
        super().__init__()
        self.file_paths = file_paths
        self.should_stop = False
    
    def run(self):
        """运行文档处理"""
        try:
            results = []
            total = len(self.file_paths)
            
            for i, file_path in enumerate(self.file_paths):
                if self.should_stop:
                    break
                
                # 发送进度信号
                self.progress_updated.emit(i + 1, total, file_path.name)
                
                # 处理文档
                result = document_service.process_document(file_path)
                results.append(result)
                
                # 发送单个文档处理完成信号
                self.document_processed.emit(result)
                
                # 让出CPU时间
                self.msleep(10)
            
            # 发送批量处理完成信号
            self.batch_completed.emit(results)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def stop(self):
        """停止处理"""
        self.should_stop = True


class DocumentManager(QObject):
    """文档管理器"""
    
    # 信号定义
    documents_loaded = Signal(list)  # 文档列表加载完成
    document_added = Signal(Document)  # 文档添加完成
    document_updated = Signal(Document)  # 文档更新完成
    document_deleted = Signal(int)  # 文档删除完成
    
    processing_started = Signal(int)  # 开始处理，参数为文档总数
    processing_progress = Signal(int, int, str)  # 处理进度
    processing_completed = Signal(int, int)  # 处理完成，成功数，总数
    processing_error = Signal(str)  # 处理错误
    
    search_completed = Signal(list)  # 搜索完成
    statistics_updated = Signal(dict)  # 统计信息更新
    
    def __init__(self):
        super().__init__()
        
        # 数据缓存
        self._all_documents: List[Document] = []
        self._filtered_documents: List[Document] = []
        self._statistics: Dict[str, Any] = {}
        
        # 处理线程
        self._processing_thread: Optional[DocumentProcessingThread] = None
        
        # 定时器用于定期刷新
        self._refresh_timer = QTimer()
        self._refresh_timer.timeout.connect(self.refresh_statistics)
        self._refresh_timer.start(30000)  # 30秒刷新一次统计
    
    def load_all_documents(self) -> List[Document]:
        """加载所有文档"""
        try:
            session = db_manager.get_session()
            documents = session.query(Document).order_by(Document.updated_at.desc()).all()
            
            self._all_documents = documents
            self._filtered_documents = documents.copy()
            
            self.documents_loaded.emit(documents)
            return documents
            
        except Exception as e:
            self.processing_error.emit(f"加载文档失败: {str(e)}")
            return []
        finally:
            session.close()
    
    def get_document_by_id(self, document_id: int) -> Optional[Document]:
        """根据ID获取文档"""
        try:
            return document_service.get_document_by_id(document_id)
        except Exception as e:
            self.processing_error.emit(f"获取文档失败: {str(e)}")
            return None
    
    def search_documents(self, keyword: str) -> List[Document]:
        """搜索文档"""
        try:
            if not keyword.strip():
                documents = self._all_documents
            else:
                documents = document_service.search_documents(keyword)
            
            self._filtered_documents = documents
            self.search_completed.emit(documents)
            return documents
            
        except Exception as e:
            self.processing_error.emit(f"搜索文档失败: {str(e)}")
            return []
    
    def filter_documents(self, filter_func: Callable[[Document], bool]) -> List[Document]:
        """过滤文档"""
        try:
            filtered = [doc for doc in self._all_documents if filter_func(doc)]
            self._filtered_documents = filtered
            return filtered
        except Exception as e:
            self.processing_error.emit(f"过滤文档失败: {str(e)}")
            return []
    
    def get_documents_by_type(self, document_type: str) -> List[Document]:
        """根据类型获取文档"""
        try:
            documents = document_service.get_documents_by_type(document_type)
            self._filtered_documents = documents
            return documents
        except Exception as e:
            self.processing_error.emit(f"获取文档失败: {str(e)}")
            return []
    
    def add_documents(self, file_paths: List[Path]):
        """添加文档（异步处理）"""
        if self._processing_thread and self._processing_thread.isRunning():
            self.processing_error.emit("已有文档正在处理中，请等待完成")
            return
        
        # 创建处理线程
        self._processing_thread = DocumentProcessingThread(file_paths)
        
        # 连接信号
        self._processing_thread.progress_updated.connect(self.processing_progress)
        self._processing_thread.document_processed.connect(self._on_document_processed)
        self._processing_thread.batch_completed.connect(self._on_batch_completed)
        self._processing_thread.error_occurred.connect(self.processing_error)
        
        # 发送开始信号
        self.processing_started.emit(len(file_paths))
        
        # 启动线程
        self._processing_thread.start()
    
    def add_folder(self, folder_path: Path, recursive: bool = True):
        """添加文件夹中的文档"""
        try:
            # 扫描文件夹
            documents_info = file_scanner.scan_for_documents([folder_path], recursive=recursive)
            file_paths = [Path(doc['file_path']) for doc in documents_info]
            
            if file_paths:
                self.add_documents(file_paths)
            else:
                self.processing_error.emit("文件夹中没有找到支持的文档文件")
                
        except Exception as e:
            self.processing_error.emit(f"扫描文件夹失败: {str(e)}")
    
    def delete_document(self, document_id: int) -> bool:
        """删除文档"""
        try:
            session = db_manager.get_session()
            document = session.query(Document).filter(Document.id == document_id).first()
            
            if document:
                session.delete(document)
                session.commit()
                
                # 从缓存中移除
                self._all_documents = [doc for doc in self._all_documents if doc.id != document_id]
                self._filtered_documents = [doc for doc in self._filtered_documents if doc.id != document_id]
                
                self.document_deleted.emit(document_id)
                return True
            else:
                self.processing_error.emit("文档不存在")
                return False
                
        except Exception as e:
            self.processing_error.emit(f"删除文档失败: {str(e)}")
            return False
        finally:
            session.close()
    
    def delete_documents(self, document_ids: List[int]) -> int:
        """批量删除文档"""
        success_count = 0
        for doc_id in document_ids:
            if self.delete_document(doc_id):
                success_count += 1
        return success_count
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            stats = document_service.get_processing_statistics()
            self._statistics = stats
            self.statistics_updated.emit(stats)
            return stats
        except Exception as e:
            self.processing_error.emit(f"获取统计信息失败: {str(e)}")
            return {}
    
    def refresh_statistics(self):
        """刷新统计信息"""
        self.get_statistics()
    
    def stop_processing(self):
        """停止当前处理"""
        if self._processing_thread and self._processing_thread.isRunning():
            self._processing_thread.stop()
            self._processing_thread.wait(5000)  # 等待5秒
    
    def _on_document_processed(self, result: DocumentProcessingResult):
        """单个文档处理完成"""
        if result.success and result.document_id:
            # 获取新添加的文档
            document = self.get_document_by_id(result.document_id)
            if document:
                # 添加到缓存
                self._all_documents.append(document)
                self._filtered_documents.append(document)
                self.document_added.emit(document)
    
    def _on_batch_completed(self, results: List[DocumentProcessingResult]):
        """批量处理完成"""
        success_count = sum(1 for r in results if r.success)
        total_count = len(results)
        
        self.processing_completed.emit(success_count, total_count)
        
        # 刷新统计信息
        self.refresh_statistics()
    
    @property
    def all_documents(self) -> List[Document]:
        """获取所有文档"""
        return self._all_documents.copy()
    
    @property
    def filtered_documents(self) -> List[Document]:
        """获取过滤后的文档"""
        return self._filtered_documents.copy()
    
    @property
    def statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._statistics.copy()
    
    def cleanup(self):
        """清理资源"""
        self._refresh_timer.stop()
        self.stop_processing()


# 全局文档管理器实例
document_manager = DocumentManager()
