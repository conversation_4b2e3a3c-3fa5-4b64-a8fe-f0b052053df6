"""
应用程序入口
Application Entry Point

启动 ikonw 个人文档管理系统的图形界面
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入后端模块
from backend.core.database import db_manager, init_database
from frontend.ui.main_window import MainWindow


class DocumentManagerApp:
    """文档管理应用程序类"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
    
    def initialize(self):
        """初始化应用程序"""
        # 创建 QApplication
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("ikonw")
        self.app.setApplicationDisplayName("ikonw - 个人文档管理系统")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("ikonw")
        
        # 设置应用程序图标（如果有的话）
        # icon_path = project_root / "resources" / "icon.png"
        # if icon_path.exists():
        #     self.app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置高DPI支持
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        return True
    
    def setup_database(self):
        """设置数据库"""
        try:
            # 初始化数据库
            if not init_database():
                QMessageBox.critical(None, "数据库错误", 
                                   "无法初始化数据库，程序将退出。")
                return False
            
            # 检查数据库连接
            db_info = db_manager.get_database_info()
            if not db_info.get('exists', False):
                QMessageBox.warning(None, "数据库警告", 
                                  "数据库文件不存在，将创建新的数据库。")
            
            return True
            
        except Exception as e:
            QMessageBox.critical(None, "数据库错误", 
                               f"数据库初始化失败: {str(e)}")
            return False
    
    def create_main_window(self):
        """创建主窗口"""
        try:
            self.main_window = MainWindow()
            return True
        except Exception as e:
            QMessageBox.critical(None, "界面错误", 
                               f"创建主窗口失败: {str(e)}")
            return False
    
    def run(self):
        """运行应用程序"""
        # 初始化应用程序
        if not self.initialize():
            return 1
        
        # 设置数据库
        if not self.setup_database():
            return 1
        
        # 创建主窗口
        if not self.create_main_window():
            return 1
        
        # 显示主窗口
        self.main_window.show()
        
        # 运行事件循环
        return self.app.exec()


def main():
    """主函数"""
    try:
        app = DocumentManagerApp()
        return app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"程序运行出错: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
