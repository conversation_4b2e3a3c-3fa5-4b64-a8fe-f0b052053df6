# 个人文档管理系统 (Personal Document Management System)

一个基于Python 3.12 + PySide6开发的个人文档管理软件，用于高效分析和管理电脑上存储的各类文档。

## 功能特性

### 核心功能
- **智能文件类型检测**: 通过文件头标志等方式精准识别文档类型，支持txt、pdf、epub、doc、docx、wps、ceb、ppt、pptx、md等格式
- **文档内容分析**: 自动分析文档类型（书籍、报告、总结、论文等）、提取标题、作者、出版信息等元数据
- **关键词提取**: 使用NLP技术提取文档关键词，剔除停用词，统计词频
- **智能摘要**: 自动生成文档摘要，支持中英文
- **重复文档检测**: 基于内容相似度检测重复文档，支持选择性删除
- **关键词统计**: 全局关键词统计和文档关联

### 技术特性
- **跨平台支持**: 优先支持Windows，考虑跨平台兼容性
- **本地数据存储**: 使用本地文件数据库，无需网络连接
- **多语言界面**: 支持中文、英文等多语言显示
- **一键打包**: 支持生成MSI安装程序

## 技术栈

- **后端**: Python 3.12
- **前端**: PySide6
- **项目管理**: Poetry
- **数据库**: SQLite/DuckDB
- **NLP**: jieba, NLTK等
- **文档解析**: PyPDF2, python-docx, markdown等

## 项目结构

```
ikonw/
├── backend/                 # 后端代码
│   ├── core/               # 核心业务逻辑
│   ├── models/             # 数据模型
│   ├── services/           # 服务层
│   ├── utils/              # 工具类
│   └── __init__.py
├── frontend/               # 前端代码
│   ├── ui/                 # UI界面
│   ├── components/         # 组件
│   ├── resources/          # 资源文件
│   └── __init__.py
├── tests/                  # 测试代码
│   ├── backend/
│   ├── frontend/
│   └── integration/
├── docs/                   # 文档
├── scripts/                # 脚本文件
├── locales/                # 多语言文件
├── pyproject.toml          # Poetry配置
├── README.md
└── LICENSE
```

## 开发环境设置

1. 确保安装Python 3.12
2. 安装Poetry: `pip install poetry`
3. 安装依赖: `poetry install`
4. 激活虚拟环境: `poetry shell`

## 许可证

MIT License
