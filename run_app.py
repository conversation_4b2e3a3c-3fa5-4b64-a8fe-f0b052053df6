#!/usr/bin/env python3
"""
ikonw 个人文档管理系统启动脚本
ikonw Personal Document Management System Launcher

运行完整的图形界面应用程序
"""

import sys
import os
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    try:
        print("正在启动 ikonw 个人文档管理系统...")
        print("=" * 50)
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要 Python 3.8 或更高版本")
            return 1
        
        # 检查依赖
        try:
            import PySide6
            print(f"✓ PySide6 版本: {PySide6.__version__}")
        except ImportError:
            print("错误: 未安装 PySide6，请运行: pip install PySide6")
            return 1
        
        try:
            import sqlalchemy
            print(f"✓ SQLAlchemy 版本: {sqlalchemy.__version__}")
        except ImportError:
            print("错误: 未安装 SQLAlchemy，请运行: pip install sqlalchemy")
            return 1
        
        # 导入并运行应用程序
        from frontend.app import DocumentManagerApp
        
        print("✓ 依赖检查完成")
        print("✓ 正在启动图形界面...")
        print("=" * 50)
        
        app = DocumentManagerApp()
        return app.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
