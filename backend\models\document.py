"""
文档数据模型
Document Data Models

定义文档相关的数据库表结构
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, ForeignKey, Table
from sqlalchemy.orm import relationship
from backend.core.database import Base

# 文档和关键词的多对多关系表
document_keywords = Table(
    'document_keywords',
    Base.metadata,
    Column('document_id', Integer, ForeignKey('documents.id'), primary_key=True),
    Column('keyword_id', Integer, ForeignKey('keywords.id'), primary_key=True)
)

class Document(Base):
    """文档信息表"""
    __tablename__ = 'documents'
    
    # 基本信息
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_path = Column(String(500), nullable=False, unique=True, comment="文件完整路径")
    file_name = Column(String(255), nullable=False, comment="文件名")
    file_extension = Column(String(10), nullable=False, comment="文件扩展名")
    file_size = Column(Integer, nullable=False, comment="文件大小（字节）")
    file_hash = Column(String(64), nullable=False, comment="文件MD5哈希值")
    
    # 文件类型信息
    detected_type = Column(String(50), nullable=False, comment="检测到的文件类型")
    mime_type = Column(String(100), comment="MIME类型")
    
    # 文档内容信息
    document_type = Column(String(50), comment="文档类型（书籍、报告、论文等）")
    title = Column(String(500), comment="文档标题")
    author = Column(String(200), comment="作者")
    publisher = Column(String(200), comment="出版社")
    publish_date = Column(String(50), comment="出版日期")
    language = Column(String(20), comment="文档语言")
    page_count = Column(Integer, comment="页数")
    word_count = Column(Integer, comment="字数")
    
    # 内容分析
    summary = Column(Text, comment="文档摘要")
    summary_chinese = Column(Text, comment="中文摘要")
    content_preview = Column(Text, comment="内容预览（前1000字符）")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    last_accessed = Column(DateTime, comment="最后访问时间")
    
    # 处理状态
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    processing_error = Column(Text, comment="处理错误信息")
    
    # 关系
    keywords = relationship("Keyword", secondary=document_keywords, back_populates="documents")
    duplicates = relationship("DocumentDuplicate", foreign_keys="DocumentDuplicate.document_id", back_populates="document")
    
    def __repr__(self):
        return f"<Document(id={self.id}, title='{self.title}', file_path='{self.file_path}')>"


class Keyword(Base):
    """关键词表"""
    __tablename__ = 'keywords'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    word = Column(String(100), nullable=False, unique=True, comment="关键词")
    frequency = Column(Integer, default=0, comment="在所有文档中的出现次数")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    # 关系
    documents = relationship("Document", secondary=document_keywords, back_populates="keywords")
    
    def __repr__(self):
        return f"<Keyword(id={self.id}, word='{self.word}', frequency={self.frequency})>"


class DocumentKeyword(Base):
    """文档关键词关联表（带权重）"""
    __tablename__ = 'document_keyword_weights'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    document_id = Column(Integer, ForeignKey('documents.id'), nullable=False)
    keyword_id = Column(Integer, ForeignKey('keywords.id'), nullable=False)
    frequency = Column(Integer, nullable=False, comment="在该文档中的出现次数")
    weight = Column(Float, nullable=False, comment="权重分数")
    
    def __repr__(self):
        return f"<DocumentKeyword(document_id={self.document_id}, keyword_id={self.keyword_id}, frequency={self.frequency})>"


class DocumentDuplicate(Base):
    """重复文档表"""
    __tablename__ = 'document_duplicates'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    document_id = Column(Integer, ForeignKey('documents.id'), nullable=False)
    duplicate_document_id = Column(Integer, ForeignKey('documents.id'), nullable=False)
    similarity_score = Column(Float, nullable=False, comment="相似度分数（0-1）")
    similarity_type = Column(String(50), nullable=False, comment="相似度类型（内容、标题、哈希等）")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    # 关系
    document = relationship("Document", foreign_keys=[document_id])
    duplicate_document = relationship("Document", foreign_keys=[duplicate_document_id])
    
    def __repr__(self):
        return f"<DocumentDuplicate(document_id={self.document_id}, duplicate_id={self.duplicate_document_id}, score={self.similarity_score})>"


class ProcessingLog(Base):
    """处理日志表"""
    __tablename__ = 'processing_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    document_id = Column(Integer, ForeignKey('documents.id'), nullable=True)
    operation = Column(String(100), nullable=False, comment="操作类型")
    status = Column(String(20), nullable=False, comment="状态（成功、失败、进行中）")
    message = Column(Text, comment="详细信息")
    processing_time = Column(Float, comment="处理时间（秒）")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    def __repr__(self):
        return f"<ProcessingLog(id={self.id}, operation='{self.operation}', status='{self.status}')>"


class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = 'system_configs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(100), nullable=False, unique=True, comment="配置键")
    value = Column(Text, comment="配置值")
    description = Column(String(200), comment="配置描述")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    def __repr__(self):
        return f"<SystemConfig(key='{self.key}', value='{self.value}')>"
