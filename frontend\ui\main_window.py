"""
主窗口界面
Main Window UI

个人文档管理系统的主界面
"""

import sys
from pathlib import Path
from typing import Optional, List, Dict
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QSplitter, QTreeWidget, QTreeWidgetItem,
    QTableWidget, QTableWidgetItem, QTextEdit, QLabel, QPushButton,
    QLineEdit, QComboBox, QProgressBar, QMessageBox, QFileDialog,
    QGroupBox, QTabWidget, QScrollArea
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, QSize
from PySide6.QtGui import QAction, QIcon, QFont, QPixmap

# 导入后端服务
sys.path.append(str(Path(__file__).parent.parent.parent))
from backend.services.document_service import DocumentService, document_service
from backend.models.document import Document
from backend.core.database import db_manager

# 导入前端组件
from frontend.components.document_list import DocumentListWidget
from frontend.components.search_filter import SearchFilterWidget
from frontend.components.document_preview import DocumentPreviewWidget
from frontend.components.batch_operations import BatchOperationsDialog

# 导入前端服务
from frontend.services.document_manager import document_manager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    document_selected = Signal(int)  # 文档选中信号
    search_requested = Signal(str)   # 搜索请求信号
    
    def __init__(self):
        super().__init__()

        # 初始化文档管理器
        self.document_manager = document_manager
        
        # 界面组件
        self.central_widget = None
        self.main_splitter = None
        self.left_panel = None
        self.right_panel = None
        self.search_filter_panel = None
        self.document_table = None
        self.preview_area = None
        self.search_bar = None
        self.filter_combo = None
        self.status_label = None
        self.progress_bar = None
        
        # 数据
        self.current_documents: List[Document] = []
        self.selected_document: Optional[Document] = None
        
        # 初始化界面
        self.init_ui()
        self.init_connections()
        self.load_initial_data()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("ikonw - 个人文档管理系统")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建中央部件
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 设置样式
        self.set_style()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 添加文档
        add_action = QAction("添加文档(&A)", self)
        add_action.setShortcut("Ctrl+A")
        add_action.setStatusTip("添加新文档到系统")
        add_action.triggered.connect(self.add_documents)
        file_menu.addAction(add_action)
        
        # 添加文件夹
        add_folder_action = QAction("添加文件夹(&F)", self)
        add_folder_action.setShortcut("Ctrl+Shift+A")
        add_folder_action.setStatusTip("批量添加文件夹中的文档")
        add_folder_action.triggered.connect(self.add_folder)
        file_menu.addAction(add_folder_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.setStatusTip("退出程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 搜索
        search_action = QAction("搜索(&S)", self)
        search_action.setShortcut("Ctrl+F")
        search_action.setStatusTip("搜索文档")
        search_action.triggered.connect(self.focus_search)
        edit_menu.addAction(search_action)
        
        # 刷新
        refresh_action = QAction("刷新(&R)", self)
        refresh_action.setShortcut("F5")
        refresh_action.setStatusTip("刷新文档列表")
        refresh_action.triggered.connect(self.refresh_documents)
        edit_menu.addAction(refresh_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 显示预览
        preview_action = QAction("显示预览(&P)", self)
        preview_action.setCheckable(True)
        preview_action.setChecked(True)
        preview_action.setStatusTip("显示/隐藏文档预览")
        preview_action.triggered.connect(self.toggle_preview)
        view_menu.addAction(preview_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        # 批量操作
        batch_action = QAction("批量操作(&B)", self)
        batch_action.setShortcut("Ctrl+B")
        batch_action.setStatusTip("批量处理文档")
        batch_action.triggered.connect(self.show_batch_operations)
        tools_menu.addAction(batch_action)

        tools_menu.addSeparator()

        # 重复文档检测
        duplicate_action = QAction("重复文档检测(&D)", self)
        duplicate_action.setStatusTip("检测并管理重复文档")
        duplicate_action.triggered.connect(self.detect_duplicates)
        tools_menu.addAction(duplicate_action)

        # 数据库管理
        db_action = QAction("数据库管理(&M)", self)
        db_action.setStatusTip("数据库维护和管理")
        db_action.triggered.connect(self.manage_database)
        tools_menu.addAction(db_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于本程序")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # 添加文档按钮
        add_btn = QPushButton("添加文档")
        add_btn.setToolTip("添加新文档")
        add_btn.clicked.connect(self.add_documents)
        toolbar.addWidget(add_btn)
        
        # 添加文件夹按钮
        add_folder_btn = QPushButton("添加文件夹")
        add_folder_btn.setToolTip("批量添加文件夹")
        add_folder_btn.clicked.connect(self.add_folder)
        toolbar.addWidget(add_folder_btn)
        
        toolbar.addSeparator()
        
        # 搜索框
        search_label = QLabel("搜索:")
        toolbar.addWidget(search_label)
        
        self.search_bar = QLineEdit()
        self.search_bar.setPlaceholderText("输入关键词搜索文档...")
        self.search_bar.setMinimumWidth(200)
        self.search_bar.returnPressed.connect(self.perform_search)
        toolbar.addWidget(self.search_bar)
        
        # 搜索按钮
        search_btn = QPushButton("搜索")
        search_btn.clicked.connect(self.perform_search)
        toolbar.addWidget(search_btn)
        
        toolbar.addSeparator()
        
        # 过滤器
        filter_label = QLabel("类型:")
        toolbar.addWidget(filter_label)
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "全部", "书籍", "论文", "报告", "总结", "手册", "其他"
        ])
        self.filter_combo.currentTextChanged.connect(self.apply_filter)
        toolbar.addWidget(self.filter_combo)
        
        toolbar.addSeparator()

        # 批量操作按钮
        batch_btn = QPushButton("批量操作")
        batch_btn.setToolTip("批量处理文档")
        batch_btn.clicked.connect(self.show_batch_operations)
        toolbar.addWidget(batch_btn)

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.setToolTip("刷新文档列表")
        refresh_btn.clicked.connect(self.refresh_documents)
        toolbar.addWidget(refresh_btn)
    
    def create_central_widget(self):
        """创建中央部件"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 主布局
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建主分割器（垂直分割：搜索面板 + 内容面板）
        main_vertical_splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(main_vertical_splitter)

        # 创建搜索过滤面板
        self.create_search_filter_panel()
        main_vertical_splitter.addWidget(self.search_filter_panel)

        # 创建内容分割器（水平分割：文档列表 + 预览面板）
        self.main_splitter = QSplitter(Qt.Horizontal)
        main_vertical_splitter.addWidget(self.main_splitter)

        # 创建左侧面板（文档列表）
        self.create_left_panel()

        # 创建右侧面板（文档预览）
        self.create_right_panel()

        # 设置分割器比例
        main_vertical_splitter.setSizes([200, 600])  # 搜索面板:内容面板
        self.main_splitter.setSizes([800, 600])      # 文档列表:预览面板

    def create_search_filter_panel(self):
        """创建搜索过滤面板"""
        self.search_filter_panel = SearchFilterWidget()

        # 连接信号
        self.search_filter_panel.search_requested.connect(self.on_search_requested)
        self.search_filter_panel.filter_changed.connect(self.on_filter_changed)
    
    def create_left_panel(self):
        """创建左侧面板"""
        self.left_panel = QWidget()
        left_layout = QVBoxLayout(self.left_panel)
        
        # 文档列表标题
        list_label = QLabel("文档列表")
        list_label.setFont(QFont("", 12, QFont.Bold))
        left_layout.addWidget(list_label)
        
        # 创建文档表格
        self.create_document_table()
        left_layout.addWidget(self.document_table)
        
        self.main_splitter.addWidget(self.left_panel)
    
    def create_right_panel(self):
        """创建右侧面板"""
        # 使用新的文档预览组件
        self.right_panel = DocumentPreviewWidget()

        # 连接信号
        self.right_panel.keyword_clicked.connect(self.on_keyword_clicked)

        self.main_splitter.addWidget(self.right_panel)
    
    def create_document_table(self):
        """创建文档表格"""
        self.document_table = DocumentListWidget()

        # 连接信号
        self.document_table.document_selected.connect(self.on_document_selected_new)
        self.document_table.document_double_clicked.connect(self.on_document_double_clicked)
        self.document_table.documents_selection_changed.connect(self.on_documents_selection_changed)
    

    def create_status_bar(self):
        """创建状态栏"""
        status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_bar.addPermanentWidget(self.progress_bar)
        
        # 文档统计
        self.stats_label = QLabel("文档: 0")
        status_bar.addPermanentWidget(self.stats_label)
    
    def set_style(self):
        """设置界面样式"""
        # 设置现代化样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #ddd;
                selection-background-color: #e3f2fd;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #ddd;
                font-family: "Consolas", "Monaco", monospace;
                font-size: 10pt;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 5px;
                margin: 10px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #2196f3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
            QLineEdit {
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 10pt;
            }
            QComboBox {
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 10pt;
            }
        """)

    def init_connections(self):
        """初始化信号连接"""
        # 文档选择信号
        self.document_selected.connect(self.load_document_details)

        # 搜索信号
        self.search_requested.connect(self.perform_search)

        # 文档管理器信号
        self.document_manager.documents_loaded.connect(self.on_documents_loaded)
        self.document_manager.document_added.connect(self.on_document_added)
        self.document_manager.document_deleted.connect(self.on_document_deleted)
        self.document_manager.processing_started.connect(self.on_processing_started)
        self.document_manager.processing_progress.connect(self.on_processing_progress)
        self.document_manager.processing_completed.connect(self.on_processing_completed)
        self.document_manager.processing_error.connect(self.on_processing_error)
        self.document_manager.search_completed.connect(self.on_search_completed)
        self.document_manager.statistics_updated.connect(self.on_statistics_updated)

    def load_initial_data(self):
        """加载初始数据"""
        # 确保数据库已初始化
        try:
            from backend.core.database import init_database
            if not init_database():
                self.show_error("数据库初始化失败")
                return
        except Exception as e:
            self.show_error(f"数据库初始化出错: {str(e)}")
            return

        # 使用文档管理器加载数据
        self.document_manager.load_all_documents()
        self.document_manager.get_statistics()

    # === 事件处理方法 ===

    def on_document_selected_new(self, document: Document):
        """新的文档选择事件处理"""
        self.selected_document = document
        self.document_selected.emit(document.id)

    def on_document_double_clicked(self, document: Document):
        """文档双击事件处理"""
        # 打开文档详情或预览
        self.selected_document = document
        self.document_selected.emit(document.id)
        # 可以在这里添加打开文档的逻辑

    def on_documents_selection_changed(self, documents: List[Document]):
        """多选文档变化事件处理"""
        # 处理多选文档的逻辑
        if documents:
            self.show_message(f"已选择 {len(documents)} 个文档")

    def on_search_requested(self, criteria: Dict):
        """搜索请求事件处理"""
        keyword = criteria.get("keyword", "")
        if keyword:
            self.document_manager.search_documents(keyword)
        else:
            # 显示所有文档
            self.current_documents = self.document_manager.all_documents
            self.document_table.load_documents(self.current_documents)
            self.update_stats()

    def on_filter_changed(self, criteria: Dict):
        """过滤条件变化事件处理"""
        if not criteria:
            # 清空过滤，显示所有文档
            self.current_documents = self.document_manager.all_documents
            self.document_table.load_documents(self.current_documents)
            self.update_stats()
            return

        try:
            # 创建过滤函数
            filter_func = self.search_filter_panel.create_filter_function(criteria)

            # 应用过滤
            filtered_documents = self.document_manager.filter_documents(filter_func)

            self.current_documents = filtered_documents
            self.document_table.load_documents(filtered_documents)
            self.update_stats()

            self.show_message(f"过滤完成，显示 {len(filtered_documents)} 个文档")

        except Exception as e:
            self.show_error(f"应用过滤时出错: {str(e)}")

    def on_document_selected(self):
        """旧的文档选择事件处理（保持兼容性）"""
        current_row = self.document_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_documents):
            document = self.current_documents[current_row]
            self.selected_document = document
            self.document_selected.emit(document.id)

    def load_document_details(self, document_id: int):
        """加载文档详细信息"""
        if not self.selected_document:
            return

        document = self.selected_document

        # 使用新的预览组件加载文档
        self.right_panel.load_document(document)

    def on_keyword_clicked(self, keyword: str):
        """关键词点击事件处理"""
        # 在搜索框中设置关键词并执行搜索
        if hasattr(self.search_filter_panel, 'keyword_edit'):
            self.search_filter_panel.keyword_edit.setText(keyword)
            self.search_filter_panel.perform_search()
        else:
            # 如果搜索过滤面板没有关键词输入框，使用工具栏的搜索框
            self.search_bar.setText(keyword)
            self.perform_search()



    def format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    # === 菜单和工具栏动作 ===

    def add_documents(self):
        """添加文档"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("文档文件 (*.pdf *.doc *.docx *.txt *.md *.rtf)")

        if file_dialog.exec():
            file_paths = file_dialog.selectedFiles()
            self.document_manager.add_documents([Path(path) for path in file_paths])

    def add_folder(self):
        """添加文件夹"""
        folder_dialog = QFileDialog(self)
        folder_dialog.setFileMode(QFileDialog.Directory)

        if folder_dialog.exec():
            folder_path = folder_dialog.selectedFiles()[0]
            self.document_manager.add_folder(Path(folder_path))

    def process_files(self, file_paths: List[Path]):
        """处理文件列表"""
        if not file_paths:
            return

        self.show_progress(f"正在处理 {len(file_paths)} 个文件...")

        # 这里应该在后台线程中处理
        # 为了简化，暂时在主线程中处理
        try:
            results = self.document_service.process_documents_batch(file_paths)

            success_count = sum(1 for r in results if r.success)
            self.show_message(f"处理完成: 成功 {success_count}/{len(file_paths)}")

            # 刷新文档列表
            self.refresh_documents()

        except Exception as e:
            self.show_error(f"处理文件时出错: {str(e)}")
        finally:
            self.hide_progress()

    def process_folder(self, folder_path: Path):
        """处理文件夹"""
        if not folder_path.exists():
            self.show_error("文件夹不存在")
            return

        # 扫描文件夹中的文档
        from backend.core.file_scanner import file_scanner

        self.show_progress("正在扫描文件夹...")

        try:
            documents = file_scanner.scan_for_documents([folder_path], recursive=True)
            file_paths = [Path(doc['file_path']) for doc in documents]

            if file_paths:
                self.process_files(file_paths)
            else:
                self.show_message("文件夹中没有找到支持的文档文件")

        except Exception as e:
            self.show_error(f"扫描文件夹时出错: {str(e)}")
        finally:
            self.hide_progress()

    def focus_search(self):
        """聚焦搜索框"""
        self.search_bar.setFocus()
        self.search_bar.selectAll()

    def perform_search(self):
        """执行搜索"""
        keyword = self.search_bar.text().strip()
        if not keyword:
            self.refresh_documents()
            return

        self.show_progress("正在搜索...")

        try:
            # 使用后端搜索
            documents = self.document_service.search_documents(keyword)
            self.current_documents = documents

            # 使用新的文档列表组件
            self.document_table.load_documents(documents)
            self.update_stats()

            self.show_message(f"找到 {len(documents)} 个匹配的文档")

        except Exception as e:
            self.show_error(f"搜索时出错: {str(e)}")
        finally:
            self.hide_progress()

    def apply_filter(self):
        """应用过滤器"""
        filter_type = self.filter_combo.currentText()

        if filter_type == "全部":
            self.refresh_documents()
            return

        self.show_progress("正在过滤...")

        try:
            if filter_type == "其他":
                # 获取所有文档，然后过滤出不属于已知类型的
                all_docs = self.get_all_documents()
                known_types = ["书籍", "论文", "报告", "总结", "手册"]
                documents = [doc for doc in all_docs if doc.document_type not in known_types]
            else:
                documents = self.document_service.get_documents_by_type(filter_type)

            self.current_documents = documents
            self.update_document_table()
            self.update_stats()

            self.show_message(f"找到 {len(documents)} 个 {filter_type} 类型的文档")

        except Exception as e:
            self.show_error(f"过滤时出错: {str(e)}")
        finally:
            self.hide_progress()

    def refresh_documents(self):
        """刷新文档列表"""
        self.document_manager.load_all_documents()

    def get_all_documents(self) -> List[Document]:
        """获取所有文档"""
        try:
            session = db_manager.get_session()
            documents = session.query(Document).all()
            return documents
        except Exception as e:
            print(f"获取文档列表失败: {e}")
            return []
        finally:
            session.close()

    def update_document_table(self):
        """更新文档表格"""
        self.document_table.load_documents(self.current_documents)

    def update_stats(self):
        """更新统计信息"""
        count = len(self.current_documents)
        self.stats_label.setText(f"文档: {count}")

    def toggle_preview(self, checked: bool):
        """切换预览面板显示"""
        self.right_panel.setVisible(checked)

    def show_batch_operations(self):
        """显示批量操作对话框"""
        if not self.current_documents:
            self.show_message("没有可操作的文档")
            return

        dialog = BatchOperationsDialog(self.current_documents, self)
        dialog.exec()

    def detect_duplicates(self):
        """检测重复文档"""
        self.show_message("重复文档检测功能开发中...")

    def manage_database(self):
        """数据库管理"""
        self.show_message("数据库管理功能开发中...")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于 ikonw",
                         "ikonw - 个人文档管理系统\n\n"
                         "版本: 1.0.0\n"
                         "基于 Python 3.12 + PySide6 开发\n\n"
                         "功能特性:\n"
                         "• 智能文档类型检测\n"
                         "• 多格式文档解析\n"
                         "• NLP 关键词提取\n"
                         "• 重复文档检测\n"
                         "• 全文搜索和过滤")

    # === 辅助方法 ===

    def show_progress(self, message: str):
        """显示进度"""
        self.status_label.setText(message)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        QApplication.processEvents()

    def hide_progress(self):
        """隐藏进度"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")

    def show_message(self, message: str):
        """显示消息"""
        self.status_label.setText(message)
        # 3秒后恢复就绪状态
        QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))

    def show_error(self, error_message: str):
        """显示错误消息"""
        QMessageBox.critical(self, "错误", error_message)
        self.status_label.setText("就绪")

    # === 文档管理器事件处理 ===

    def on_documents_loaded(self, documents: List[Document]):
        """文档加载完成事件"""
        self.current_documents = documents
        self.document_table.load_documents(documents)
        self.update_stats()
        self.show_message(f"已加载 {len(documents)} 个文档")

    def on_document_added(self, document: Document):
        """文档添加完成事件"""
        # 文档已经在处理线程中添加到缓存，这里只需要刷新界面
        pass

    def on_document_deleted(self, document_id: int):
        """文档删除完成事件"""
        # 刷新当前显示的文档列表
        self.current_documents = self.document_manager.filtered_documents
        self.document_table.load_documents(self.current_documents)
        self.update_stats()
        self.show_message("文档删除成功")

    def on_processing_started(self, total_count: int):
        """处理开始事件"""
        self.show_progress(f"开始处理 {total_count} 个文档...")
        self.progress_bar.setRange(0, total_count)
        self.progress_bar.setValue(0)

    def on_processing_progress(self, current: int, total: int, filename: str):
        """处理进度事件"""
        self.progress_bar.setValue(current)
        self.status_label.setText(f"正在处理: {filename} ({current}/{total})")

    def on_processing_completed(self, success_count: int, total_count: int):
        """处理完成事件"""
        self.hide_progress()

        # 刷新文档列表
        self.current_documents = self.document_manager.all_documents
        self.document_table.load_documents(self.current_documents)
        self.update_stats()

        self.show_message(f"处理完成: 成功 {success_count}/{total_count}")

    def on_processing_error(self, error_message: str):
        """处理错误事件"""
        self.hide_progress()
        self.show_error(error_message)

    def on_search_completed(self, documents: List[Document]):
        """搜索完成事件"""
        self.current_documents = documents
        self.document_table.load_documents(documents)
        self.update_stats()

    def on_statistics_updated(self, statistics: Dict):
        """统计信息更新事件"""
        # 可以在这里更新状态栏的统计信息
        total = statistics.get('total_documents', 0)
        processed = statistics.get('processed_documents', 0)
        self.stats_label.setText(f"文档: {len(self.current_documents)}/{total} (已处理: {processed})")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 清理文档管理器资源
        self.document_manager.cleanup()
        event.accept()
